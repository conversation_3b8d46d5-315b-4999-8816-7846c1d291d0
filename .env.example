# Database (Update with your Supabase credentials from 'supabase status')
DATABASE_URL="postgresql://postgres:postgres@localhost:54322/postgres"
DIRECT_URL="postgresql://postgres:postgres@localhost:54322/postgres"

# NextAuth
NEXTAUTH_SECRET="your-nextauth-secret-at-least-32-characters-long"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (Get from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Supabase Local (Update with your local Supabase credentials from 'supabase status')
NEXT_PUBLIC_SUPABASE_URL="http://localhost:54321"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-local-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-local-supabase-service-role-key"

# Production Supabase (uncomment and update for production deployment)
# NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
# NEXT_PUBLIC_SUPABASE_ANON_KEY="your-production-anon-key"
# SUPABASE_SERVICE_ROLE_KEY="your-production-service-role-key"
# DATABASE_URL="postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres"
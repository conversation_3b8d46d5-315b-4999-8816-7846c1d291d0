{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/page": "app/page.js", "/browse/page": "app/browse/page.js", "/api/novels/route": "app/api/novels/route.js", "/library/page": "app/library/page.js", "/profile/page": "app/profile/page.js", "/api/library/route": "app/api/library/route.js", "/auth/signin/page": "app/auth/signin/page.js", "/auth/error/page": "app/auth/error/page.js", "/_not-found/page": "app/_not-found/page.js"}
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/route";
exports.ids = ["app/api/novels/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/route.ts */ \"(rsc)/./src/app/api/novels/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/route\",\n        pathname: \"/api/novels\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZub3ZlbHMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRm5vdmVscyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRm5vdmVscyUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRndlZXJhd2F0JTJGRGVza3RvcCUyRmFkYy1wbGF0Zm9ybSUyRnNlcnZpY2VzJTJGY29udGVudCUyRmJsYWNrLWJsb2clMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGd2VlcmF3YXQlMkZEZXNrdG9wJTJGYWRjLXBsYXRmb3JtJTJGc2VydmljZXMlMkZjb250ZW50JTJGYmxhY2stYmxvZyZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDNEM7QUFDekg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLz81NmE2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi9Vc2Vycy93ZWVyYXdhdC9EZXNrdG9wL2FkYy1wbGF0Zm9ybS9zZXJ2aWNlcy9jb250ZW50L2JsYWNrLWJsb2cvc3JjL2FwcC9hcGkvbm92ZWxzL3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9ub3ZlbHMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9ub3ZlbHNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL25vdmVscy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIi9Vc2Vycy93ZWVyYXdhdC9EZXNrdG9wL2FkYy1wbGF0Zm9ybS9zZXJ2aWNlcy9jb250ZW50L2JsYWNrLWJsb2cvc3JjL2FwcC9hcGkvbm92ZWxzL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9ub3ZlbHMvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/novels/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n    const genre = searchParams.get(\"genre\");\n    const search = searchParams.get(\"search\");\n    const status = searchParams.get(\"status\") || _prisma_client__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.PUBLISHED;\n    const skip = (page - 1) * limit;\n    const where = {\n        status: status,\n        ...genre && {\n            genre\n        },\n        ...search && {\n            OR: [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    author: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ]\n        }\n    };\n    try {\n        const [novels, total] = await Promise.all([\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findMany({\n                where,\n                include: {\n                    author: {\n                        select: {\n                            id: true,\n                            name: true,\n                            image: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            chapters: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                skip,\n                take: limit\n            }),\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.count({\n                where\n            })\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novels,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching novels:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch novels\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const body = await request.json();\n        const { title, description, synopsis, genre, tags } = body;\n        // Validate required fields\n        if (!title || title.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required\"\n            }, {\n                status: 400\n            });\n        }\n        const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.create({\n            data: {\n                title: title.trim(),\n                description: description?.trim() || null,\n                synopsis: synopsis?.trim() || null,\n                genre: genre?.trim() || null,\n                tags: tags || [],\n                authorId: session.user.id,\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.DRAFT\n            },\n            include: {\n                author: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                },\n                _count: {\n                    select: {\n                        chapters: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(novel, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create novel\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.READER;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            try {\n                // Auto-assign READER role to new users\n                if (account?.provider === \"google\" && profile?.email) {\n                    const existingUser = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            email: profile.email\n                        }\n                    });\n                    if (!existingUser) {\n                        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.user.create({\n                            data: {\n                                email: profile.email,\n                                name: profile.name,\n                                image: profile.picture,\n                                role: _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.READER\n                            }\n                        });\n                    }\n                }\n                return true;\n            } catch (error) {\n                console.error(\"Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
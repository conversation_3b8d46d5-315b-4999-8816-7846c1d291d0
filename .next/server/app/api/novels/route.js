"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/route";
exports.ids = ["app/api/novels/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/route.ts */ \"(rsc)/./src/app/api/novels/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/route\",\n        pathname: \"/api/novels\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/novels/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n    const genre = searchParams.get(\"genre\");\n    const search = searchParams.get(\"search\");\n    const status = searchParams.get(\"status\") || _prisma_client__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.PUBLISHED;\n    const skip = (page - 1) * limit;\n    const where = {\n        status: status,\n        ...genre && {\n            genre\n        },\n        ...search && {\n            OR: [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    author: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ]\n        }\n    };\n    try {\n        const [novels, total] = await Promise.all([\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findMany({\n                where,\n                include: {\n                    author: {\n                        select: {\n                            id: true,\n                            name: true,\n                            image: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            chapters: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                skip,\n                take: limit\n            }),\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.count({\n                where\n            })\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novels,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching novels:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch novels\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const body = await request.json();\n        const { title, description, synopsis, genre, tags } = body;\n        // Validate required fields\n        if (!title || title.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required\"\n            }, {\n                status: 400\n            });\n        }\n        const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.create({\n            data: {\n                title: title.trim(),\n                description: description?.trim() || null,\n                synopsis: synopsis?.trim() || null,\n                genre: genre?.trim() || null,\n                tags: tags || [],\n                authorId: session.user.id,\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.DRAFT\n            },\n            include: {\n                author: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                },\n                _count: {\n                    select: {\n                        chapters: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(novel, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create novel\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9ub3ZlbHMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXVEO0FBQ1g7QUFDSjtBQUNQO0FBQ1c7QUFFckMsZUFBZUssSUFBSUMsT0FBb0I7SUFDNUMsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJRixRQUFRRyxHQUFHO0lBQzVDLE1BQU1DLE9BQU9DLFNBQVNKLGFBQWFLLEdBQUcsQ0FBQyxXQUFXO0lBQ2xELE1BQU1DLFFBQVFGLFNBQVNKLGFBQWFLLEdBQUcsQ0FBQyxZQUFZO0lBQ3BELE1BQU1FLFFBQVFQLGFBQWFLLEdBQUcsQ0FBQztJQUMvQixNQUFNRyxTQUFTUixhQUFhSyxHQUFHLENBQUM7SUFDaEMsTUFBTUksU0FBU1QsYUFBYUssR0FBRyxDQUFDLGFBQWFSLHVEQUFXQSxDQUFDYSxTQUFTO0lBRWxFLE1BQU1DLE9BQU8sQ0FBQ1IsT0FBTyxLQUFLRztJQUUxQixNQUFNTSxRQUFRO1FBQ1pILFFBQVFBO1FBQ1IsR0FBSUYsU0FBUztZQUFFQTtRQUFNLENBQUM7UUFDdEIsR0FBSUMsVUFBVTtZQUNaSyxJQUFJO2dCQUNGO29CQUFFQyxPQUFPO3dCQUFFQyxVQUFVUDt3QkFBUVEsTUFBTTtvQkFBdUI7Z0JBQUU7Z0JBQzVEO29CQUFFQyxhQUFhO3dCQUFFRixVQUFVUDt3QkFBUVEsTUFBTTtvQkFBdUI7Z0JBQUU7Z0JBQ2xFO29CQUFFRSxRQUFRO3dCQUFFQyxNQUFNOzRCQUFFSixVQUFVUDs0QkFBUVEsTUFBTTt3QkFBdUI7b0JBQUU7Z0JBQUU7YUFDeEU7UUFDSCxDQUFDO0lBQ0g7SUFFQSxJQUFJO1FBQ0YsTUFBTSxDQUFDSSxRQUFRQyxNQUFNLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO1lBQ3hDM0IsMkNBQU1BLENBQUM0QixLQUFLLENBQUNDLFFBQVEsQ0FBQztnQkFDcEJiO2dCQUNBYyxTQUFTO29CQUNQUixRQUFRO3dCQUFFUyxRQUFROzRCQUFFQyxJQUFJOzRCQUFNVCxNQUFNOzRCQUFNVSxPQUFPO3dCQUFLO29CQUFFO29CQUN4REMsUUFBUTt3QkFBRUgsUUFBUTs0QkFBRUksVUFBVTt3QkFBSztvQkFBRTtnQkFDdkM7Z0JBQ0FDLFNBQVM7b0JBQUVDLFdBQVc7Z0JBQU87Z0JBQzdCdEI7Z0JBQ0F1QixNQUFNNUI7WUFDUjtZQUNBViwyQ0FBTUEsQ0FBQzRCLEtBQUssQ0FBQ1csS0FBSyxDQUFDO2dCQUFFdkI7WUFBTTtTQUM1QjtRQUVELE9BQU9uQixxREFBWUEsQ0FBQzJDLElBQUksQ0FBQztZQUN2QmhCO1lBQ0FpQixZQUFZO2dCQUNWbEM7Z0JBQ0FHO2dCQUNBZTtnQkFDQWlCLE9BQU9DLEtBQUtDLElBQUksQ0FBQ25CLFFBQVFmO1lBQzNCO1FBQ0Y7SUFDRixFQUFFLE9BQU9tQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQ3hDLE9BQU9oRCxxREFBWUEsQ0FBQzJDLElBQUksQ0FDdEI7WUFBRUssT0FBTztRQUF5QixHQUNsQztZQUFFaEMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFTyxlQUFla0MsS0FBSzVDLE9BQW9CO0lBQzdDLE1BQU02QyxVQUFVLE1BQU1sRCwyREFBZ0JBLENBQUNDLGtEQUFXQTtJQUVsRCxJQUFJLENBQUNpRCxTQUFTQyxRQUFRRCxRQUFRQyxJQUFJLENBQUNDLElBQUksS0FBSyxVQUFVO1FBQ3BELE9BQU9yRCxxREFBWUEsQ0FBQzJDLElBQUksQ0FBQztZQUFFSyxPQUFPO1FBQWUsR0FBRztZQUFFaEMsUUFBUTtRQUFJO0lBQ3BFO0lBRUEsSUFBSTtRQUNGLE1BQU1zQyxPQUFPLE1BQU1oRCxRQUFRcUMsSUFBSTtRQUMvQixNQUFNLEVBQUV0QixLQUFLLEVBQUVHLFdBQVcsRUFBRStCLFFBQVEsRUFBRXpDLEtBQUssRUFBRTBDLElBQUksRUFBRSxHQUFHRjtRQUV0RCwyQkFBMkI7UUFDM0IsSUFBSSxDQUFDakMsU0FBU0EsTUFBTW9DLElBQUksR0FBR0MsTUFBTSxLQUFLLEdBQUc7WUFDdkMsT0FBTzFELHFEQUFZQSxDQUFDMkMsSUFBSSxDQUN0QjtnQkFBRUssT0FBTztZQUFvQixHQUM3QjtnQkFBRWhDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1lLFFBQVEsTUFBTTVCLDJDQUFNQSxDQUFDNEIsS0FBSyxDQUFDNEIsTUFBTSxDQUFDO1lBQ3RDQyxNQUFNO2dCQUNKdkMsT0FBT0EsTUFBTW9DLElBQUk7Z0JBQ2pCakMsYUFBYUEsYUFBYWlDLFVBQVU7Z0JBQ3BDRixVQUFVQSxVQUFVRSxVQUFVO2dCQUM5QjNDLE9BQU9BLE9BQU8yQyxVQUFVO2dCQUN4QkQsTUFBTUEsUUFBUSxFQUFFO2dCQUNoQkssVUFBVVYsUUFBUUMsSUFBSSxDQUFDakIsRUFBRTtnQkFDekJuQixRQUFRWix1REFBV0EsQ0FBQzBELEtBQUs7WUFDM0I7WUFDQTdCLFNBQVM7Z0JBQ1BSLFFBQVE7b0JBQUVTLFFBQVE7d0JBQUVDLElBQUk7d0JBQU1ULE1BQU07d0JBQU1VLE9BQU87b0JBQUs7Z0JBQUU7Z0JBQ3hEQyxRQUFRO29CQUFFSCxRQUFRO3dCQUFFSSxVQUFVO29CQUFLO2dCQUFFO1lBQ3ZDO1FBQ0Y7UUFFQSxPQUFPdEMscURBQVlBLENBQUMyQyxJQUFJLENBQUNaLE9BQU87WUFBRWYsUUFBUTtRQUFJO0lBQ2hELEVBQUUsT0FBT2dDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBT2hELHFEQUFZQSxDQUFDMkMsSUFBSSxDQUN0QjtZQUFFSyxPQUFPO1FBQXlCLEdBQ2xDO1lBQUVoQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvYXBwL2FwaS9ub3ZlbHMvcm91dGUudHM/NjZkYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvc2VydmVyXCJcbmltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tIFwibmV4dC1hdXRoXCJcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSBcIkAvbGliL2F1dGhcIlxuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSBcIkAvbGliL2RiXCJcbmltcG9ydCB7IE5vdmVsU3RhdHVzIH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCJcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybClcbiAgY29uc3QgcGFnZSA9IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoXCJwYWdlXCIpIHx8IFwiMVwiKVxuICBjb25zdCBsaW1pdCA9IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoXCJsaW1pdFwiKSB8fCBcIjEyXCIpXG4gIGNvbnN0IGdlbnJlID0gc2VhcmNoUGFyYW1zLmdldChcImdlbnJlXCIpXG4gIGNvbnN0IHNlYXJjaCA9IHNlYXJjaFBhcmFtcy5nZXQoXCJzZWFyY2hcIilcbiAgY29uc3Qgc3RhdHVzID0gc2VhcmNoUGFyYW1zLmdldChcInN0YXR1c1wiKSB8fCBOb3ZlbFN0YXR1cy5QVUJMSVNIRURcblxuICBjb25zdCBza2lwID0gKHBhZ2UgLSAxKSAqIGxpbWl0XG5cbiAgY29uc3Qgd2hlcmUgPSB7XG4gICAgc3RhdHVzOiBzdGF0dXMgYXMgTm92ZWxTdGF0dXMsXG4gICAgLi4uKGdlbnJlICYmIHsgZ2VucmUgfSksXG4gICAgLi4uKHNlYXJjaCAmJiB7XG4gICAgICBPUjogW1xuICAgICAgICB7IHRpdGxlOiB7IGNvbnRhaW5zOiBzZWFyY2gsIG1vZGU6IFwiaW5zZW5zaXRpdmVcIiBhcyBjb25zdCB9IH0sXG4gICAgICAgIHsgZGVzY3JpcHRpb246IHsgY29udGFpbnM6IHNlYXJjaCwgbW9kZTogXCJpbnNlbnNpdGl2ZVwiIGFzIGNvbnN0IH0gfSxcbiAgICAgICAgeyBhdXRob3I6IHsgbmFtZTogeyBjb250YWluczogc2VhcmNoLCBtb2RlOiBcImluc2Vuc2l0aXZlXCIgYXMgY29uc3QgfSB9IH0sXG4gICAgICBdLFxuICAgIH0pLFxuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBbbm92ZWxzLCB0b3RhbF0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICBwcmlzbWEubm92ZWwuZmluZE1hbnkoe1xuICAgICAgICB3aGVyZSxcbiAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgIGF1dGhvcjogeyBzZWxlY3Q6IHsgaWQ6IHRydWUsIG5hbWU6IHRydWUsIGltYWdlOiB0cnVlIH0gfSxcbiAgICAgICAgICBfY291bnQ6IHsgc2VsZWN0OiB7IGNoYXB0ZXJzOiB0cnVlIH0gfSxcbiAgICAgICAgfSxcbiAgICAgICAgb3JkZXJCeTogeyBjcmVhdGVkQXQ6IFwiZGVzY1wiIH0sXG4gICAgICAgIHNraXAsXG4gICAgICAgIHRha2U6IGxpbWl0LFxuICAgICAgfSksXG4gICAgICBwcmlzbWEubm92ZWwuY291bnQoeyB3aGVyZSB9KSxcbiAgICBdKVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIG5vdmVscyxcbiAgICAgIHBhZ2luYXRpb246IHtcbiAgICAgICAgcGFnZSxcbiAgICAgICAgbGltaXQsXG4gICAgICAgIHRvdGFsLFxuICAgICAgICBwYWdlczogTWF0aC5jZWlsKHRvdGFsIC8gbGltaXQpLFxuICAgICAgfSxcbiAgICB9KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBub3ZlbHM6XCIsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6IFwiRmFpbGVkIHRvIGZldGNoIG5vdmVsc1wiIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpXG5cbiAgaWYgKCFzZXNzaW9uPy51c2VyIHx8IHNlc3Npb24udXNlci5yb2xlICE9PSBcIkFVVEhPUlwiKSB7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IFwiVW5hdXRob3JpemVkXCIgfSwgeyBzdGF0dXM6IDQwMSB9KVxuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKClcbiAgICBjb25zdCB7IHRpdGxlLCBkZXNjcmlwdGlvbiwgc3lub3BzaXMsIGdlbnJlLCB0YWdzIH0gPSBib2R5XG5cbiAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcbiAgICBpZiAoIXRpdGxlIHx8IHRpdGxlLnRyaW0oKS5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogXCJUaXRsZSBpcyByZXF1aXJlZFwiIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIGNvbnN0IG5vdmVsID0gYXdhaXQgcHJpc21hLm5vdmVsLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIHRpdGxlOiB0aXRsZS50cmltKCksXG4gICAgICAgIGRlc2NyaXB0aW9uOiBkZXNjcmlwdGlvbj8udHJpbSgpIHx8IG51bGwsXG4gICAgICAgIHN5bm9wc2lzOiBzeW5vcHNpcz8udHJpbSgpIHx8IG51bGwsXG4gICAgICAgIGdlbnJlOiBnZW5yZT8udHJpbSgpIHx8IG51bGwsXG4gICAgICAgIHRhZ3M6IHRhZ3MgfHwgW10sXG4gICAgICAgIGF1dGhvcklkOiBzZXNzaW9uLnVzZXIuaWQsXG4gICAgICAgIHN0YXR1czogTm92ZWxTdGF0dXMuRFJBRlQsXG4gICAgICB9LFxuICAgICAgaW5jbHVkZToge1xuICAgICAgICBhdXRob3I6IHsgc2VsZWN0OiB7IGlkOiB0cnVlLCBuYW1lOiB0cnVlLCBpbWFnZTogdHJ1ZSB9IH0sXG4gICAgICAgIF9jb3VudDogeyBzZWxlY3Q6IHsgY2hhcHRlcnM6IHRydWUgfSB9LFxuICAgICAgfSxcbiAgICB9KVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKG5vdmVsLCB7IHN0YXR1czogMjAxIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNyZWF0aW5nIG5vdmVsOlwiLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiBcIkZhaWxlZCB0byBjcmVhdGUgbm92ZWxcIiB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59Il0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImdldFNlcnZlclNlc3Npb24iLCJhdXRoT3B0aW9ucyIsInByaXNtYSIsIk5vdmVsU3RhdHVzIiwiR0VUIiwicmVxdWVzdCIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsInBhZ2UiLCJwYXJzZUludCIsImdldCIsImxpbWl0IiwiZ2VucmUiLCJzZWFyY2giLCJzdGF0dXMiLCJQVUJMSVNIRUQiLCJza2lwIiwid2hlcmUiLCJPUiIsInRpdGxlIiwiY29udGFpbnMiLCJtb2RlIiwiZGVzY3JpcHRpb24iLCJhdXRob3IiLCJuYW1lIiwibm92ZWxzIiwidG90YWwiLCJQcm9taXNlIiwiYWxsIiwibm92ZWwiLCJmaW5kTWFueSIsImluY2x1ZGUiLCJzZWxlY3QiLCJpZCIsImltYWdlIiwiX2NvdW50IiwiY2hhcHRlcnMiLCJvcmRlckJ5IiwiY3JlYXRlZEF0IiwidGFrZSIsImNvdW50IiwianNvbiIsInBhZ2luYXRpb24iLCJwYWdlcyIsIk1hdGgiLCJjZWlsIiwiZXJyb3IiLCJjb25zb2xlIiwiUE9TVCIsInNlc3Npb24iLCJ1c2VyIiwicm9sZSIsImJvZHkiLCJzeW5vcHNpcyIsInRhZ3MiLCJ0cmltIiwibGVuZ3RoIiwiY3JlYXRlIiwiZGF0YSIsImF1dGhvcklkIiwiRFJBRlQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.READER;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
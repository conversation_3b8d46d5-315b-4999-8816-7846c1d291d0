# Black Blogs - Novel Writing & Reading Platform

A modern full-stack web application for authors to write and publish novels, and readers to discover and enjoy content.

## 🚀 Technology Stack

- **Framework:** Next.js 14+ with App Router
- **Styling:** Tailwind CSS with shadcn/ui components
- **Database:** Supabase (PostgreSQL)
- **ORM:** Prisma
- **Authentication:** NextAuth.js v5 with Google OAuth
- **State Management:** Redux Toolkit with RTK Query
- **File Storage:** Supabase Storage
- **Deployment:** Vercel (recommended)

## 📋 Features

### Public Features (No Authentication Required)
- 🏠 Homepage with featured/popular novels
- 🔍 Browse page with search and filtering
- 📖 Novel detail pages with synopsis, chapters, author info
- 📚 Chapter reading interface with navigation

### Reader Features (Authenticated Users)
- 📚 Personal library to save favorite novels
- ❤️ Add/remove novels from library
- 📖 Reading progress tracking

### Author Features (AUTHOR Role Required)
- 📊 Author dashboard with novel analytics
- ✍️ Novel creation and editing interface
- 📝 Chapter management (create, edit, reorder, publish/unpublish)
- 🖋️ Rich text editor for writing chapters
- 🖼️ Cover image upload functionality

## 🏗️ Project Structure

```
black-blog/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Auth-protected routes
│   │   ├── (public)/          # Public routes
│   │   ├── api/               # API routes
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Homepage
│   ├── components/            # Reusable components
│   │   ├── ui/               # shadcn/ui components
│   │   ├── auth/             # Authentication components
│   │   ├── novel/            # Novel-related components
│   │   └── layout/           # Layout components
│   ├── lib/                  # Utility functions
│   │   ├── auth.ts           # NextAuth configuration
│   │   ├── db.ts             # Database connection
│   │   ├── store.ts          # Redux store
│   │   └── utils.ts          # Helper functions
│   ├── store/                # Redux slices and API
│   └── types/                # TypeScript type definitions
├── prisma/                   # Database schema and migrations
├── public/                   # Static assets
└── docs/                     # Project documentation
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+ and npm/yarn/pnpm
- Git
- Supabase account
- Google OAuth credentials

### Installation

1. **Install dependencies:**
```bash
npm install
```

2. **Environment setup:**
```bash
cp .env.example .env.local
# Fill in your environment variables
```

3. **Database setup:**
```bash
npx prisma generate
npx prisma db push
```

4. **Run development server:**
```bash
npm run dev
```

## 🔧 Environment Variables

Create a `.env.local` file with the following variables:

```env
# Database
DATABASE_URL="your-supabase-database-url"
DIRECT_URL="your-supabase-direct-url"

# NextAuth
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"
```

## 📊 Database Schema

The application uses the following main entities:
- **User:** Authentication and profile information
- **Novel:** Book metadata and content
- **Chapter:** Individual chapters within novels
- **Library:** User's saved novels (many-to-many relationship)

## 🚀 Deployment

### Vercel Deployment
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Database Migration
```bash
npx prisma migrate deploy
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
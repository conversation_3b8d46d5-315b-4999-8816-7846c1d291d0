# Black Blogs - Complete Setup Guide

## 🚀 Quick Start (Recommended)

The fastest way to get Black Blogs running locally:

```bash
# 1. Clone the repository
git clone <your-repo-url>
cd black-blog

# 2. Run the automated setup
./scripts/quick-start.sh

# 3. Update environment variables
# Edit .env.local with your Google OAuth credentials

# 4. Start development
npm run dev
```

## 📋 Prerequisites

Before starting, ensure you have:

- **Node.js 18+** installed
- **Docker** installed and running (for Supabase local development)
- **Git** installed
- **Supabase account** (free at [supabase.com](https://supabase.com))
- **Google Cloud Console account** (for OAuth)

## 🛠️ Detailed Setup Steps

### Step 1: Project Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd black-blog

# Install dependencies
npm install
```

### Step 2: Supabase Setup with CLI

```bash
# Run the Supabase setup script
./scripts/setup-supabase.sh
```

This script will:
- Install Supabase CLI if needed
- Login to your Supabase account
- Initialize Supabase in your project
- Start local Supabase development environment
- Display connection credentials

### Step 3: Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local
```

Update `.env.local` with the credentials from the Supabase setup:

```env
# Database (from 'supabase status')
DATABASE_URL="postgresql://postgres:postgres@localhost:54322/postgres"
DIRECT_URL="postgresql://postgres:postgres@localhost:54322/postgres"

# NextAuth
NEXTAUTH_SECRET="your-secure-secret-at-least-32-characters"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Supabase Local (from 'supabase status')
NEXT_PUBLIC_SUPABASE_URL="http://localhost:54321"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-local-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-local-service-role-key"
```

### Step 4: Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized JavaScript origins: `http://localhost:3000`
   - Authorized redirect URIs: `http://localhost:3000/api/auth/callback/google`
5. Copy Client ID and Client Secret to `.env.local`

### Step 5: Database Setup

```bash
# Generate Prisma client
npx prisma generate

# Push schema to local database
npx prisma db push
```

### Step 6: Start Development

```bash
# Start Next.js development server
npm run dev
```

Your application will be available at:
- **App:** http://localhost:3000
- **Supabase Studio:** http://localhost:54323
- **Prisma Studio:** `npx prisma studio`

## 🔧 Available Scripts

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Database
```bash
npx prisma studio    # Open Prisma Studio
npx prisma generate  # Generate Prisma client
npx prisma db push   # Push schema to database
npx prisma db pull   # Pull schema from database
```

### Supabase
```bash
supabase status      # Check local Supabase status
supabase studio      # Open Supabase Studio
supabase stop        # Stop local Supabase
supabase start       # Start local Supabase
supabase logs        # View logs
```

## 🌐 Production Deployment

### 1. Create Production Supabase Project

```bash
# Create new project
supabase projects create black-blogs

# Link local to production
supabase link --project-ref your-project-ref

# Deploy schema
supabase db push
```

### 2. Deploy to Vercel

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel

# Add environment variables in Vercel dashboard
```

### 3. Update Google OAuth

Add production URLs to Google OAuth:
- Authorized origins: `https://yourdomain.com`
- Redirect URIs: `https://yourdomain.com/api/auth/callback/google`

## 🐛 Troubleshooting

### Common Issues

#### Supabase won't start
```bash
# Check Docker
docker ps

# Stop all containers and restart
docker stop $(docker ps -aq)
supabase start
```

#### Database connection errors
```bash
# Reset local database
supabase db reset

# Push schema again
npx prisma db push
```

#### Port conflicts
```bash
# Check what's using ports
lsof -i :3000
lsof -i :54321

# Kill processes or use different ports
supabase start --db-port 54322 --api-port 54321
```

#### Authentication not working
1. Check Google OAuth credentials
2. Verify redirect URLs
3. Check NEXTAUTH_SECRET is set
4. Ensure NEXTAUTH_URL matches your domain

### Getting Help

- **Supabase CLI:** `supabase help`
- **Prisma:** `npx prisma --help`
- **Next.js:** [Next.js Documentation](https://nextjs.org/docs)
- **Project Issues:** Check GitHub issues or create a new one

## 📚 Additional Resources

- [Supabase Setup Guide](docs/SUPABASE_SETUP.md) - Detailed Supabase CLI guide
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) - Production deployment
- [Testing Guide](docs/TESTING_GUIDE.md) - Testing procedures
- [API Documentation](docs/API_ROUTES.md) - API reference

## ✅ Verification Checklist

After setup, verify everything works:

- [ ] Application loads at http://localhost:3000
- [ ] Supabase Studio accessible at http://localhost:54323
- [ ] Google OAuth sign-in works
- [ ] Database operations work (create user, novel, etc.)
- [ ] No console errors in browser
- [ ] All environment variables set correctly

## 🎉 You're Ready!

Your Black Blogs platform is now set up and ready for development. Start by:

1. Creating your first user account via Google OAuth
2. Exploring the author dashboard
3. Creating your first novel
4. Testing the reading interface

Happy coding! 🚀

# API Routes Documentation

This document outlines all API routes for the Black Blogs platform.

## Authentication Routes

### NextAuth.js Routes
- `GET/POST /api/auth/[...nextauth]` - NextAuth.js handler

## Novel Routes

### Public Novel Routes
- `GET /api/novels` - Get all published novels with pagination and filtering
- `GET /api/novels/[id]` - Get novel details by ID
- `GET /api/novels/[id]/chapters` - Get all published chapters for a novel
- `GET /api/novels/featured` - Get featured novels for homepage

### Author Novel Routes (Protected)
- `POST /api/novels` - Create a new novel (AUTHOR only)
- `PUT /api/novels/[id]` - Update novel (AUTHOR only, own novels)
- `DELETE /api/novels/[id]` - Delete novel (AUTHOR only, own novels)
- `GET /api/author/novels` - Get author's own novels

## Chapter Routes

### Public Chapter Routes
- `GET /api/chapters/[id]` - Get chapter content by ID (published only)

### Author Chapter Routes (Protected)
- `POST /api/novels/[novelId]/chapters` - Create new chapter (AUTHOR only)
- `PUT /api/chapters/[id]` - Update chapter (AUTHOR only, own chapters)
- `DELETE /api/chapters/[id]` - Delete chapter (AUTHOR only, own chapters)
- `PUT /api/chapters/[id]/reorder` - Reorder chapters (AUTHOR only)
- `PUT /api/chapters/[id]/publish` - Publish/unpublish chapter (AUTHOR only)

## Library Routes (Protected)

- `GET /api/library` - Get user's library
- `POST /api/library` - Add novel to library
- `DELETE /api/library/[novelId]` - Remove novel from library
- `GET /api/library/check/[novelId]` - Check if novel is in library

## User Routes (Protected)

- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `PUT /api/user/role` - Update user role (admin only)

## File Upload Routes (Protected)

- `POST /api/upload/cover` - Upload novel cover image (AUTHOR only)
- `DELETE /api/upload/cover/[filename]` - Delete cover image (AUTHOR only)

## Search Routes

- `GET /api/search/novels` - Search novels by title, author, genre, tags
- `GET /api/search/authors` - Search authors by name

## Example API Route Implementation

### Novel CRUD API

```typescript
// src/app/api/novels/route.ts
import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { NovelStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get("page") || "1")
  const limit = parseInt(searchParams.get("limit") || "12")
  const genre = searchParams.get("genre")
  const search = searchParams.get("search")
  const status = searchParams.get("status") || NovelStatus.PUBLISHED

  const skip = (page - 1) * limit

  const where = {
    status: status as NovelStatus,
    ...(genre && { genre }),
    ...(search && {
      OR: [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { author: { name: { contains: search, mode: "insensitive" } } },
      ],
    }),
  }

  try {
    const [novels, total] = await Promise.all([
      prisma.novel.findMany({
        where,
        include: {
          author: { select: { id: true, name: true } },
          _count: { select: { chapters: true } },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.novel.count({ where }),
    ])

    return NextResponse.json({
      novels,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to fetch novels" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "AUTHOR") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { title, description, synopsis, genre, tags } = body

    const novel = await prisma.novel.create({
      data: {
        title,
        description,
        synopsis,
        genre,
        tags: tags || [],
        authorId: session.user.id,
        status: NovelStatus.DRAFT,
      },
      include: {
        author: { select: { id: true, name: true } },
        _count: { select: { chapters: true } },
      },
    })

    return NextResponse.json(novel, { status: 201 })
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to create novel" },
      { status: 500 }
    )
  }
}
```

### Chapter API Route

```typescript
// src/app/api/novels/[novelId]/chapters/route.ts
import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { ChapterStatus } from "@prisma/client"

export async function GET(
  request: NextRequest,
  { params }: { params: { novelId: string } }
) {
  const { novelId } = params
  const session = await getServerSession(authOptions)

  try {
    // Check if user is the author (can see all chapters) or public user (published only)
    const novel = await prisma.novel.findUnique({
      where: { id: novelId },
      select: { authorId: true, status: true },
    })

    if (!novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    const isAuthor = session?.user?.id === novel.authorId
    const where = {
      novelId,
      ...(isAuthor ? {} : { status: ChapterStatus.PUBLISHED }),
    }

    const chapters = await prisma.chapter.findMany({
      where,
      orderBy: { order: "asc" },
      select: {
        id: true,
        title: true,
        order: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        ...(isAuthor ? { content: true } : {}),
      },
    })

    return NextResponse.json(chapters)
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to fetch chapters" },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { novelId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "AUTHOR") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { novelId } = params

  try {
    // Verify novel ownership
    const novel = await prisma.novel.findUnique({
      where: { id: novelId, authorId: session.user.id },
    })

    if (!novel) {
      return NextResponse.json(
        { error: "Novel not found or unauthorized" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const { title, content } = body

    // Get next order number
    const lastChapter = await prisma.chapter.findFirst({
      where: { novelId },
      orderBy: { order: "desc" },
      select: { order: true },
    })

    const order = (lastChapter?.order || 0) + 1

    const chapter = await prisma.chapter.create({
      data: {
        title,
        content,
        order,
        novelId,
        status: ChapterStatus.DRAFT,
      },
    })

    return NextResponse.json(chapter, { status: 201 })
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to create chapter" },
      { status: 500 }
    )
  }
}
```
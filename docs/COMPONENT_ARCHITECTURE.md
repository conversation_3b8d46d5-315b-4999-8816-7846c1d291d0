# Component Architecture Guide

This document outlines the component structure and architecture patterns for the Black Blogs platform.

## Component Organization

```
src/components/
├── ui/                     # shadcn/ui base components
│   ├── button.tsx
│   ├── input.tsx
│   ├── card.tsx
│   ├── dialog.tsx
│   └── ...
├── layout/                 # Layout components
│   ├── header.tsx
│   ├── footer.tsx
│   ├── sidebar.tsx
│   └── navigation.tsx
├── auth/                   # Authentication components
│   ├── signin-button.tsx
│   ├── auth-guard.tsx
│   ├── role-guard.tsx
│   └── signin-form.tsx
├── novel/                  # Novel-related components
│   ├── novel-card.tsx
│   ├── novel-list.tsx
│   ├── novel-detail.tsx
│   ├── novel-form.tsx
│   └── novel-search.tsx
├── chapter/                # Chapter components
│   ├── chapter-reader.tsx
│   ├── chapter-list.tsx
│   ├── chapter-form.tsx
│   ├── chapter-editor.tsx
│   └── chapter-navigation.tsx
├── library/                # Library components
│   ├── library-button.tsx
│   ├── library-list.tsx
│   └── library-stats.tsx
├── author/                 # Author-specific components
│   ├── author-dashboard.tsx
│   ├── author-stats.tsx
│   ├── novel-manager.tsx
│   └── chapter-manager.tsx
├── common/                 # Shared components
│   ├── loading-spinner.tsx
│   ├── error-boundary.tsx
│   ├── pagination.tsx
│   ├── search-bar.tsx
│   └── theme-toggle.tsx
└── providers/              # Context providers
    ├── session-provider.tsx
    ├── theme-provider.tsx
    └── redux-provider.tsx
```

## Key Component Patterns

### 1. Layout Components

#### Header Component
```typescript
// src/components/layout/header.tsx
"use client"

import Link from "next/link"
import { useSession } from "next-auth/react"
import { SignInButton } from "@/components/auth/signin-button"
import { ThemeToggle } from "@/components/common/theme-toggle"
import { Button } from "@/components/ui/button"

export function Header() {
  const { data: session } = useSession()

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 hidden md:flex">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <span className="hidden font-bold sm:inline-block">
              Black Blogs
            </span>
          </Link>
          <nav className="flex items-center space-x-6 text-sm font-medium">
            <Link href="/browse">Browse</Link>
            {session?.user?.role === "AUTHOR" && (
              <Link href="/dashboard">Dashboard</Link>
            )}
            {session && <Link href="/library">Library</Link>}
          </nav>
        </div>
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            {/* Search component will go here */}
          </div>
          <nav className="flex items-center space-x-2">
            <ThemeToggle />
            <SignInButton />
          </nav>
        </div>
      </div>
    </header>
  )
}
```

### 2. Authentication Components

#### Auth Guard Component
```typescript
// src/components/auth/auth-guard.tsx
"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { LoadingSpinner } from "@/components/common/loading-spinner"

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requiredRole?: "READER" | "AUTHOR"
  fallback?: React.ReactNode
}

export function AuthGuard({
  children,
  requireAuth = true,
  requiredRole,
  fallback
}: AuthGuardProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return

    if (requireAuth && !session) {
      router.push("/auth/signin")
      return
    }

    if (requiredRole && session?.user?.role !== requiredRole) {
      router.push("/unauthorized")
      return
    }
  }, [session, status, requireAuth, requiredRole, router])

  if (status === "loading") {
    return <LoadingSpinner />
  }

  if (requireAuth && !session) {
    return fallback || <div>Redirecting to sign in...</div>
  }

  if (requiredRole && session?.user?.role !== requiredRole) {
    return fallback || <div>Unauthorized access</div>
  }

  return <>{children}</>
}
```

### 3. Novel Components

#### Novel Card Component
```typescript
// src/components/novel/novel-card.tsx
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { LibraryButton } from "@/components/library/library-button"
import type { Novel } from "@prisma/client"

interface NovelCardProps {
  novel: Novel & {
    author: { name: string }
    _count: { chapters: number }
  }
  showLibraryButton?: boolean
}

export function NovelCard({ novel, showLibraryButton = true }: NovelCardProps) {
  return (
    <Card className="overflow-hidden transition-shadow hover:shadow-lg">
      <CardHeader className="p-0">
        <div className="relative aspect-[3/4] w-full">
          <Image
            src={novel.coverImage || "/placeholder-cover.jpg"}
            alt={novel.title}
            fill
            className="object-cover"
          />
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <Link href={`/novels/${novel.id}`}>
          <h3 className="font-semibold line-clamp-2 hover:underline">
            {novel.title}
          </h3>
        </Link>
        <p className="text-sm text-muted-foreground mt-1">
          by {novel.author.name}
        </p>
        <p className="text-sm text-muted-foreground line-clamp-3 mt-2">
          {novel.description}
        </p>
        <div className="flex items-center gap-2 mt-3">
          {novel.genre && <Badge variant="secondary">{novel.genre}</Badge>}
          <span className="text-xs text-muted-foreground">
            {novel._count.chapters} chapters
          </span>
        </div>
      </CardContent>
      {showLibraryButton && (
        <CardFooter className="p-4 pt-0">
          <LibraryButton novelId={novel.id} />
        </CardFooter>
      )}
    </Card>
  )
}
```
# State Management with Redux Toolkit & RTK Query

This document outlines the state management architecture using Redux Toolkit and RTK Query.

## Store Configuration

### Store Setup

```typescript
// src/lib/store.ts
import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'
import { novelsApi } from '@/store/api/novelsApi'
import { chaptersApi } from '@/store/api/chaptersApi'
import { libraryApi } from '@/store/api/libraryApi'
import { authSlice } from '@/store/slices/authSlice'
import { uiSlice } from '@/store/slices/uiSlice'

export const store = configureStore({
  reducer: {
    // API slices
    [novelsApi.reducerPath]: novelsApi.reducer,
    [chaptersApi.reducerPath]: chaptersApi.reducer,
    [libraryApi.reducerPath]: libraryApi.reducer,

    // Regular slices
    auth: authSlice.reducer,
    ui: uiSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      novelsApi.middleware,
      chaptersApi.middleware,
      libraryApi.middleware
    ),
})

setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
```

### Redux Provider

```typescript
// src/components/providers/redux-provider.tsx
"use client"

import { Provider } from 'react-redux'
import { store } from '@/lib/store'

export function ReduxProvider({ children }: { children: React.ReactNode }) {
  return <Provider store={store}>{children}</Provider>
}
```

## RTK Query API Slices

### Novels API

```typescript
// src/store/api/novelsApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Novel, NovelStatus } from '@prisma/client'

export interface NovelWithAuthor extends Novel {
  author: { id: string; name: string }
  _count: { chapters: number }
}

export interface NovelsResponse {
  novels: NovelWithAuthor[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface CreateNovelRequest {
  title: string
  description?: string
  synopsis?: string
  genre?: string
  tags?: string[]
}

export const novelsApi = createApi({
  reducerPath: 'novelsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/novels',
  }),
  tagTypes: ['Novel', 'AuthorNovels'],
  endpoints: (builder) => ({
    // Public endpoints
    getNovels: builder.query<NovelsResponse, {
      page?: number
      limit?: number
      genre?: string
      search?: string
    }>({
      query: (params) => ({
        url: '',
        params,
      }),
      providesTags: ['Novel'],
    }),

    getNovel: builder.query<NovelWithAuthor, string>({
      query: (id) => `/${id}`,
      providesTags: (result, error, id) => [{ type: 'Novel', id }],
    }),

    getFeaturedNovels: builder.query<NovelWithAuthor[], void>({
      query: () => '/featured',
      providesTags: ['Novel'],
    }),

    // Author endpoints
    getAuthorNovels: builder.query<NovelWithAuthor[], void>({
      query: () => '/author',
      providesTags: ['AuthorNovels'],
    }),

    createNovel: builder.mutation<NovelWithAuthor, CreateNovelRequest>({
      query: (novel) => ({
        url: '',
        method: 'POST',
        body: novel,
      }),
      invalidatesTags: ['AuthorNovels'],
    }),

    updateNovel: builder.mutation<NovelWithAuthor, {
      id: string
      data: Partial<CreateNovelRequest>
    }>({
      query: ({ id, data }) => ({
        url: `/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Novel', id },
        'AuthorNovels',
      ],
    }),

    deleteNovel: builder.mutation<void, string>({
      query: (id) => ({
        url: `/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['AuthorNovels'],
    }),

    publishNovel: builder.mutation<NovelWithAuthor, {
      id: string
      status: NovelStatus
    }>({
      query: ({ id, status }) => ({
        url: `/${id}/publish`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Novel', id },
        'AuthorNovels',
        'Novel',
      ],
    }),
  }),
})

export const {
  useGetNovelsQuery,
  useGetNovelQuery,
  useGetFeaturedNovelsQuery,
  useGetAuthorNovelsQuery,
  useCreateNovelMutation,
  useUpdateNovelMutation,
  useDeleteNovelMutation,
  usePublishNovelMutation,
} = novelsApi
```

### Chapters API

```typescript
// src/store/api/chaptersApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Chapter, ChapterStatus } from '@prisma/client'

export interface CreateChapterRequest {
  title: string
  content: string
}

export interface UpdateChapterRequest {
  title?: string
  content?: string
  status?: ChapterStatus
}

export const chaptersApi = createApi({
  reducerPath: 'chaptersApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
  }),
  tagTypes: ['Chapter', 'NovelChapters'],
  endpoints: (builder) => ({
    getChapters: builder.query<Chapter[], string>({
      query: (novelId) => `/novels/${novelId}/chapters`,
      providesTags: (result, error, novelId) => [
        { type: 'NovelChapters', id: novelId },
      ],
    }),

    getChapter: builder.query<Chapter, string>({
      query: (id) => `/chapters/${id}`,
      providesTags: (result, error, id) => [{ type: 'Chapter', id }],
    }),

    createChapter: builder.mutation<Chapter, {
      novelId: string
      data: CreateChapterRequest
    }>({
      query: ({ novelId, data }) => ({
        url: `/novels/${novelId}/chapters`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { novelId }) => [
        { type: 'NovelChapters', id: novelId },
      ],
    }),

    updateChapter: builder.mutation<Chapter, {
      id: string
      data: UpdateChapterRequest
    }>({
      query: ({ id, data }) => ({
        url: `/chapters/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Chapter', id },
      ],
    }),

    deleteChapter: builder.mutation<void, string>({
      query: (id) => ({
        url: `/chapters/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['NovelChapters'],
    }),

    reorderChapters: builder.mutation<void, {
      novelId: string
      chapters: { id: string; order: number }[]
    }>({
      query: ({ novelId, chapters }) => ({
        url: `/novels/${novelId}/chapters/reorder`,
        method: 'PUT',
        body: { chapters },
      }),
      invalidatesTags: (result, error, { novelId }) => [
        { type: 'NovelChapters', id: novelId },
      ],
    }),
  }),
})

export const {
  useGetChaptersQuery,
  useGetChapterQuery,
  useCreateChapterMutation,
  useUpdateChapterMutation,
  useDeleteChapterMutation,
  useReorderChaptersMutation,
} = chaptersApi
```
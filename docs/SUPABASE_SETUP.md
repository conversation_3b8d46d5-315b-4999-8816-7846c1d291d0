# Supabase Setup Guide with CLI

## Overview

This guide walks you through setting up Supabase for the Black Blogs platform using the Supabase CLI for both local development and production deployment.

## Prerequisites

- Node.js 18+ installed
- Git repository set up
- Supabase account (sign up at [supabase.com](https://supabase.com))

## 1. Install Supabase CLI

### Using npm (recommended)
```bash
npm install -g supabase
```

### Using Homebrew (macOS)
```bash
brew install supabase/tap/supabase
```

### Using Scoop (Windows)
```bash
scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
scoop install supabase
```

### Verify Installation
```bash
supabase --version
```

## 2. Initialize Supabase in Your Project

### Login to Supabase
```bash
supabase login
```
This will open your browser to authenticate with your Supabase account.

### Initialize Supabase in Project
```bash
# Navigate to your project directory
cd /path/to/black-blog

# Initialize Supabase
supabase init
```

This creates a `supabase/` directory with configuration files.

## 3. Local Development Setup

### Start Local Supabase
```bash
supabase start
```

This will:
- Start a local PostgreSQL database
- Start the Supabase Studio (local dashboard)
- Start the API gateway
- Start the Auth server
- Start the Storage server

### Get Local Connection Details
```bash
supabase status
```

Example output:
```
API URL: http://localhost:54321
GraphQL URL: http://localhost:54321/graphql/v1
DB URL: postgresql://postgres:postgres@localhost:54322/postgres
Studio URL: http://localhost:54323
Inbucket URL: http://localhost:54324
JWT secret: super-secret-jwt-token-with-at-least-32-characters-long
anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Update Local Environment Variables
Create `.env.local` with local Supabase credentials:
```env
# Local Supabase
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-local-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-local-service-role-key

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres
DIRECT_URL=postgresql://postgres:postgres@localhost:54322/postgres

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-local-secret

# Google OAuth (same for local and production)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 4. Database Schema Migration

### Generate Migration from Prisma Schema
```bash
# Generate Prisma client
npx prisma generate

# Create migration from schema
npx prisma migrate dev --name init

# Or push schema directly (for development)
npx prisma db push
```

### Create Supabase Migration (Alternative)
```bash
# Create a new migration
supabase migration new create_initial_schema

# Edit the migration file in supabase/migrations/
# Add your SQL schema
```

### Apply Migrations
```bash
# Apply migrations to local database
supabase db reset

# Or apply specific migration
supabase migration up
```

## 5. Production Setup

### Create Production Project
```bash
# Create new project
supabase projects create black-blogs --org-id your-org-id

# Or list existing projects
supabase projects list
```

### Link Local Project to Production
```bash
# Link to existing project
supabase link --project-ref your-project-ref

# Or create and link new project
supabase link --project-ref $(supabase projects create black-blogs --org-id your-org-id)
```

### Deploy Database Schema
```bash
# Push local schema to production
supabase db push

# Or deploy migrations
supabase migration up --db-url your-production-db-url
```

### Get Production Credentials
```bash
# Get project details
supabase projects api-keys --project-ref your-project-ref
```

## 6. Environment Configuration

### Production Environment Variables
```env
# Production Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key

# Database
DATABASE_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres
DIRECT_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres

# NextAuth
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-production-secret

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 7. Authentication Setup

### Configure Auth Providers
```bash
# Update auth configuration
supabase secrets set GOOGLE_CLIENT_ID=your-google-client-id
supabase secrets set GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### Configure Auth Settings via Dashboard
1. Go to Authentication > Settings in Supabase Dashboard
2. Add your site URL: `https://yourdomain.com`
3. Add redirect URLs:
   - `https://yourdomain.com/api/auth/callback/google`
   - `http://localhost:3000/api/auth/callback/google` (for development)

## 8. Storage Setup (Optional)

### Create Storage Buckets
```sql
-- Create bucket for cover images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('covers', 'covers', true);

-- Create policy for authenticated users
CREATE POLICY "Users can upload cover images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'covers' AND 
  auth.role() = 'authenticated'
);

CREATE POLICY "Cover images are publicly accessible" ON storage.objects
FOR SELECT USING (bucket_id = 'covers');
```

### Configure Storage in Code
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Upload function
export async function uploadCoverImage(file: File, novelId: string) {
  const fileExt = file.name.split('.').pop()
  const fileName = `${novelId}.${fileExt}`
  
  const { data, error } = await supabase.storage
    .from('covers')
    .upload(fileName, file, { upsert: true })
    
  if (error) throw error
  return data
}
```

## 9. Useful CLI Commands

### Development Commands
```bash
# Start local development
supabase start

# Stop local development
supabase stop

# Reset local database
supabase db reset

# View local logs
supabase logs

# Open local Studio
supabase studio
```

### Production Commands
```bash
# Deploy functions
supabase functions deploy

# View production logs
supabase logs --project-ref your-project-ref

# Backup database
supabase db dump --project-ref your-project-ref > backup.sql

# Restore database
supabase db reset --project-ref your-project-ref
```

### Migration Commands
```bash
# Create new migration
supabase migration new migration_name

# Apply migrations
supabase migration up

# Rollback migration
supabase migration down

# Check migration status
supabase migration list
```

## 10. Automated Setup Script

Create `scripts/setup-supabase.sh`:
```bash
#!/bin/bash

echo "🚀 Setting up Supabase for Black Blogs..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Installing Supabase CLI..."
    npm install -g supabase
fi

# Login to Supabase
echo "Please login to Supabase..."
supabase login

# Initialize Supabase (if not already done)
if [ ! -d "supabase" ]; then
    echo "Initializing Supabase..."
    supabase init
fi

# Start local development
echo "Starting local Supabase..."
supabase start

# Get local credentials
echo "Getting local credentials..."
supabase status

echo "✅ Supabase setup complete!"
echo "📝 Update your .env.local with the credentials shown above"
echo "🔗 Local Studio: http://localhost:54323"
```

Make it executable:
```bash
chmod +x scripts/setup-supabase.sh
```

## 11. Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Stop all Docker containers
docker stop $(docker ps -aq)

# Or specify custom ports
supabase start --db-port 54322 --api-port 54321
```

#### Migration Errors
```bash
# Reset and reapply migrations
supabase db reset
supabase migration up
```

#### Connection Issues
```bash
# Check Docker status
docker ps

# Restart Supabase
supabase stop
supabase start
```

### Getting Help
```bash
# Get help for any command
supabase help
supabase start --help
supabase migration --help
```

## 12. Next Steps

1. **Run the setup script:**
   ```bash
   ./scripts/setup-supabase.sh
   ```

2. **Update environment variables** with the credentials from `supabase status`

3. **Push your database schema:**
   ```bash
   npx prisma db push
   ```

4. **Start your Next.js development server:**
   ```bash
   npm run dev
   ```

5. **Access Supabase Studio** at http://localhost:54323 to manage your database

6. **For production deployment,** follow the production setup steps above

Your Supabase setup is now complete and ready for development! 🎉

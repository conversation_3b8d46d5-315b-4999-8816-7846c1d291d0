import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export default withAuth(
  function middleware(req: NextRequest) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // Allow access to auth pages
    if (pathname.startsWith("/auth/")) {
      return NextResponse.next()
    }

    // Protect dashboard routes - require AUTHOR role
    if (pathname.startsWith("/dashboard")) {
      if (!token || token.role !== "AUTHOR") {
        return NextResponse.redirect(new URL("/unauthorized", req.url))
      }
    }

    // Protect library routes - require authentication
    if (pathname.startsWith("/library")) {
      if (!token) {
        return NextResponse.redirect(new URL("/auth/signin", req.url))
      }
    }

    // Protect profile and settings routes - require authentication
    if (pathname.startsWith("/profile") || pathname.startsWith("/settings")) {
      if (!token) {
        return NextResponse.redirect(new URL("/auth/signin", req.url))
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // Allow public routes
        if (
          pathname === "/" ||
          pathname.startsWith("/browse") ||
          pathname.startsWith("/novels") ||
          pathname.startsWith("/chapters") ||
          pathname.startsWith("/auth/") ||
          pathname.startsWith("/api/auth/") ||
          pathname.startsWith("/_next/") ||
          pathname.startsWith("/favicon") ||
          pathname === "/unauthorized"
        ) {
          return true
        }

        // For protected routes, check if user is authenticated
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}
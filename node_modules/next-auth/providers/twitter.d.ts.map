{"version": 3, "file": "twitter.d.ts", "sourceRoot": "", "sources": ["../src/providers/twitter.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,GAAG,CAAA;AAErD,MAAM,WAAW,oBAAoB;IACnC,EAAE,EAAE,MAAM,CAAA;IACV,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,MAAM,CAAA;IAChB,WAAW,EAAE,MAAM,CAAA;IACnB,GAAG,EAAE,MAAM,CAAA;IACX,QAAQ,EAAE;QACR,GAAG,EAAE;YACH,IAAI,EAAE,KAAK,CAAC;gBACV,GAAG,EAAE,MAAM,CAAA;gBACX,YAAY,EAAE,MAAM,CAAA;gBACpB,WAAW,EAAE,MAAM,CAAA;gBACnB,OAAO,EAAE,MAAM,EAAE,CAAA;aAClB,CAAC,CAAA;SACH,CAAA;QACD,WAAW,EAAE;YACX,IAAI,EAAE,GAAG,EAAE,CAAA;SACZ,CAAA;KACF,CAAA;IACD,SAAS,EAAE,OAAO,CAAA;IAClB,eAAe,EAAE,MAAM,CAAA;IACvB,aAAa,EAAE,MAAM,CAAA;IACrB,YAAY,EAAE,MAAM,CAAA;IACpB,UAAU,EAAE,MAAM,CAAA;IAClB,gBAAgB,EAAE,MAAM,CAAA;IACxB,UAAU,CAAC,EAAE,GAAG,CAAA;IAChB,SAAS,CAAC,EAAE,GAAG,CAAA;IACf,WAAW,EAAE,OAAO,CAAA;IACpB,QAAQ,EAAE,OAAO,CAAA;IACjB,cAAc,EAAE,MAAM,CAAA;IACtB,IAAI,CAAC,EAAE,GAAG,CAAA;IACV,MAAM,EAAE;QACN,UAAU,EAAE,MAAM,CAAA;QAClB,EAAE,EAAE,MAAM,CAAA;QACV,MAAM,EAAE,MAAM,CAAA;QACd,IAAI,EAAE,MAAM,CAAA;QACZ,SAAS,EAAE,OAAO,CAAA;QAClB,QAAQ,EAAE;YACR,QAAQ,EAAE,GAAG,EAAE,CAAA;YACf,OAAO,EAAE,GAAG,EAAE,CAAA;YACd,aAAa,EAAE,KAAK,CAAC;gBACnB,WAAW,EAAE,MAAM,CAAA;gBACnB,IAAI,EAAE,MAAM,CAAA;gBACZ,EAAE,EAAE,MAAM,CAAA;gBACV,MAAM,EAAE,MAAM,CAAA;gBACd,OAAO,EAAE,MAAM,EAAE,CAAA;aAClB,CAAC,CAAA;YACF,IAAI,EAAE,GAAG,EAAE,CAAA;SACZ,CAAA;QACD,MAAM,EAAE,MAAM,CAAA;QACd,qBAAqB,EAAE,MAAM,CAAA;QAC7B,yBAAyB,EAAE,MAAM,CAAA;QACjC,mBAAmB,EAAE,MAAM,CAAA;QAC3B,uBAAuB,EAAE,MAAM,CAAA;QAC/B,uBAAuB,EAAE,MAAM,CAAA;QAC/B,GAAG,CAAC,EAAE,GAAG,CAAA;QACT,WAAW,CAAC,EAAE,GAAG,CAAA;QACjB,KAAK,CAAC,EAAE,GAAG,CAAA;QACX,YAAY,CAAC,EAAE,GAAG,CAAA;QAClB,eAAe,EAAE,OAAO,CAAA;QACxB,aAAa,EAAE,MAAM,CAAA;QACrB,cAAc,EAAE,MAAM,CAAA;QACtB,SAAS,EAAE,OAAO,CAAA;QAClB,SAAS,EAAE,OAAO,CAAA;QAClB,IAAI,EAAE,MAAM,CAAA;KACb,CAAA;IACD,oBAAoB,EAAE,OAAO,CAAA;IAC7B,aAAa,EAAE,OAAO,CAAA;IACtB,sBAAsB,EAAE,OAAO,CAAA;IAC/B,wBAAwB,EAAE,MAAM,CAAA;IAChC,4BAA4B,EAAE,MAAM,CAAA;IACpC,kCAAkC,EAAE,MAAM,CAAA;IAC1C,uBAAuB,EAAE,OAAO,CAAA;IAChC,iBAAiB,EAAE,MAAM,CAAA;IACzB,uBAAuB,EAAE,MAAM,CAAA;IAC/B,kBAAkB,EAAE,MAAM,CAAA;IAC1B,kBAAkB,EAAE,MAAM,CAAA;IAC1B,4BAA4B,EAAE,MAAM,CAAA;IACpC,0BAA0B,EAAE,MAAM,CAAA;IAClC,kBAAkB,EAAE,MAAM,CAAA;IAC1B,4BAA4B,EAAE,OAAO,CAAA;IACrC,oBAAoB,EAAE,OAAO,CAAA;IAC7B,eAAe,EAAE,OAAO,CAAA;IACxB,qBAAqB,EAAE,OAAO,CAAA;IAC9B,SAAS,EAAE,OAAO,CAAA;IAClB,mBAAmB,EAAE,OAAO,CAAA;IAC5B,aAAa,EAAE,OAAO,CAAA;IACtB,eAAe,EAAE,MAAM,CAAA;IACvB,qBAAqB,EAAE,GAAG,EAAE,CAAA;IAC5B,SAAS,EAAE,OAAO,CAAA;IAClB,wBAAwB,EAAE,OAAO,CAAA;CAClC;AAED,wBAAgB,aAAa,CAC3B,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,oBAAoB,EACpD,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAyB7C;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE;QACJ,EAAE,EAAE,MAAM,CAAA;QACV,IAAI,EAAE,MAAM,CAAA;QACZ,QAAQ,EAAE,MAAM,CAAA;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,QAAQ,CAAC,EAAE;YACT,GAAG,EAAE;gBACH,IAAI,EAAE,KAAK,CAAC;oBACV,KAAK,EAAE,MAAM,CAAA;oBACb,GAAG,EAAE,MAAM,CAAA;oBACX,GAAG,EAAE,MAAM,CAAA;oBACX,YAAY,EAAE,MAAM,CAAA;oBACpB,WAAW,EAAE,MAAM,CAAA;iBACpB,CAAC,CAAA;aACH,CAAA;YACD,WAAW,EAAE;gBACX,QAAQ,EAAE,KAAK,CAAC;oBACd,KAAK,EAAE,MAAM,CAAA;oBACb,GAAG,EAAE,MAAM,CAAA;oBACX,GAAG,EAAE,MAAM,CAAA;iBACZ,CAAC,CAAA;aACH,CAAA;SACF,CAAA;QACD,QAAQ,CAAC,EAAE,OAAO,CAAA;QAClB,WAAW,CAAC,EAAE,MAAM,CAAA;QACpB,GAAG,CAAC,EAAE,MAAM,CAAA;QACZ,iBAAiB,CAAC,EAAE,MAAM,CAAA;QAC1B,SAAS,CAAC,EAAE,OAAO,CAAA;QACnB,eAAe,CAAC,EAAE,MAAM,CAAA;QACxB,UAAU,CAAC,EAAE,MAAM,CAAA;KACpB,CAAA;IACD,QAAQ,CAAC,EAAE;QACT,MAAM,CAAC,EAAE,KAAK,CAAC;YACb,EAAE,EAAE,MAAM,CAAA;YACV,IAAI,EAAE,MAAM,CAAA;SACb,CAAC,CAAA;KACH,CAAA;CACF;AAED,MAAM,CAAC,OAAO,UAAU,OAAO,CAC7B,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,oBAAoB,GAAG,cAAc,EACrE,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CA4C7C"}
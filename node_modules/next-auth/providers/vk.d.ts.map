{"version": 3, "file": "vk.d.ts", "sourceRoot": "", "sources": ["../src/providers/vk.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,GAAG,CAAA;AAErD,MAAM,WAAW,SAAS;IAExB,QAAQ,EAAE,KAAK,CAAC;QACd,EAAE,EAAE,MAAM,CAAA;QACV,UAAU,EAAE,MAAM,CAAA;QAClB,SAAS,EAAE,MAAM,CAAA;QACjB,SAAS,EAAE,MAAM,CAAA;QACjB,iBAAiB,EAAE,OAAO,CAAA;QAC1B,SAAS,EAAE,OAAO,CAAA;QAClB,WAAW,CAAC,EAAE,MAAM,CAAA;QACpB,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACf,WAAW,CAAC,EAAE,MAAM,CAAA;QACpB,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACd,aAAa,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACrB,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QAChB,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QAChB,aAAa,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC7B,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,WAAW,CAAC,EAAE,MAAM,CAAA;QACpB,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,IAAI,CAAC,EAAE;YACL,EAAE,EAAE,MAAM,CAAA;YACV,KAAK,EAAE,MAAM,CAAA;SACd,CAAA;QACD,OAAO,CAAC,EAAE;YACR,EAAE,EAAE,MAAM,CAAA;YACV,KAAK,EAAE,MAAM,CAAA;SACd,CAAA;QACD,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACjB,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QAClB,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACjB,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QAChB,iBAAiB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACzB,aAAa,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACrB,WAAW,CAAC,EAAE;YACZ,QAAQ,CAAC,EAAE,MAAM,CAAA;YACjB,KAAK,CAAC,EAAE,MAAM,CAAA;YACd,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,WAAW,CAAC,EAAE,MAAM,CAAA;YACpB,SAAS,CAAC,EAAE,MAAM,CAAA;SACnB,CAAA;QACD,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,YAAY,CAAC,EAAE,OAAO,GAAG,KAAK,CAAA;QAC9B,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,EAAE,CAAC,EAAE,MAAM,CAAA;QACX,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,yBAAyB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACjC,uBAAuB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QAC/B,QAAQ,CAAC,EAAE;YACT,YAAY,CAAC,EAAE,MAAM,CAAA;YACrB,UAAU,CAAC,EAAE,MAAM,CAAA;SACpB,CAAA;QACD,IAAI,CAAC,EAAE,MAAM,CAAA;QACb,YAAY,CAAC,EAAE;YACb,UAAU,CAAC,EAAE,MAAM,CAAA;YACnB,MAAM,EAAE,MAAM,CAAA;YACd,EAAE,EAAE,MAAM,CAAA;YACV,QAAQ,EAAE,MAAM,CAAA;YAChB,KAAK,EAAE,MAAM,CAAA;YACb,GAAG,CAAC,EAAE,MAAM,CAAA;YACZ,QAAQ,EAAE,MAAM,CAAA;YAChB,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,QAAQ,CAAC,EAAE,MAAM,CAAA;YACjB,QAAQ,CAAC,EAAE,MAAM,CAAA;YACjB,SAAS,CAAC,EAAE,MAAM,CAAA;SACnB,CAAA;QACD,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,SAAS,CAAC,EAAE;YACV,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YACpC,IAAI,CAAC,EAAE,MAAM,CAAA;SACd,CAAA;QACD,OAAO,CAAC,EAAE;YACR,QAAQ,CAAC,EAAE,MAAM,CAAA;YACjB,WAAW,CAAC,EAAE,MAAM,CAAA;YACpB,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,SAAS,CAAC,EAAE,MAAM,CAAA;SACnB,CAAA;QACD,UAAU,CAAC,EAAE;YACX,KAAK,EAAE;gBACL,UAAU,CAAC,EAAE,MAAM,CAAA;gBACnB,QAAQ,EAAE,MAAM,CAAA;gBAChB,IAAI,EAAE,MAAM,CAAA;gBACZ,MAAM,CAAC,EAAE,MAAM,CAAA;gBACf,EAAE,EAAE,MAAM,CAAA;gBACV,MAAM,CAAC,EAAE,KAAK,CAAC;oBACb,MAAM,CAAC,EAAE,MAAM,CAAA;oBACf,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA;oBACtE,GAAG,CAAC,EAAE,MAAM,CAAA;oBACZ,KAAK,CAAC,EAAE,MAAM,CAAA;iBACf,CAAC,CAAA;gBACF,GAAG,CAAC,EAAE,MAAM,CAAA;gBACZ,IAAI,CAAC,EAAE,MAAM,CAAA;gBACb,QAAQ,EAAE,MAAM,CAAA;gBAChB,SAAS,CAAC,EAAE,MAAM,CAAA;gBAClB,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;gBACnB,KAAK,CAAC,EAAE,MAAM,CAAA;gBACd,OAAO,CAAC,EAAE,MAAM,CAAA;gBAChB,KAAK,CAAC,EAAE,KAAK,CAAC;oBACZ,MAAM,EAAE,MAAM,CAAA;oBACd,GAAG,EAAE,MAAM,CAAA;oBACX,GAAG,CAAC,EAAE,MAAM,CAAA;oBACZ,IAAI,EACA,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,MAAM,GACN,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,KAAK,CAAA;oBACT,KAAK,EAAE,MAAM,CAAA;iBACd,CAAC,CAAA;gBACF,IAAI,CAAC,EAAE,MAAM,CAAA;gBACb,OAAO,CAAC,EAAE,MAAM,CAAA;gBAChB,KAAK,CAAC,EAAE,MAAM,CAAA;gBACd,QAAQ,EAAE,OAAO,CAAA;aAClB,CAAA;YACD,IAAI,EAAE;gBACJ,CAAC,EAAE,MAAM,CAAA;gBACT,CAAC,EAAE,MAAM,CAAA;gBACT,EAAE,EAAE,MAAM,CAAA;gBACV,EAAE,EAAE,MAAM,CAAA;aACX,CAAA;YACD,IAAI,EAAE;gBACJ,CAAC,EAAE,MAAM,CAAA;gBACT,CAAC,EAAE,MAAM,CAAA;gBACT,EAAE,EAAE,MAAM,CAAA;gBACV,EAAE,EAAE,MAAM,CAAA;aACX,CAAA;SACF,CAAA;QACD,eAAe,CAAC,EAAE,MAAM,CAAA;QACxB,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACnB,iBAAiB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACzB,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QACnB,mBAAmB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QAC3B,YAAY,CAAC,EAAE,MAAM,CAAA;QACrB,UAAU,CAAC,EAAE;YACX,EAAE,CAAC,EAAE,MAAM,CAAA;YACX,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,IAAI,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,YAAY,CAAA;SACxC,CAAA;QACD,MAAM,CAAC,EAAE;YACP,QAAQ,CAAC,EAAE,MAAM,CAAA;YACjB,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,UAAU,CAAC,EAAE,MAAM,CAAA;YACnB,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,SAAS,CAAC,EAAE,MAAM,CAAA;YAClB,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,KAAK,CAAC,EAAE,MAAM,CAAA;YACd,QAAQ,CAAC,EAAE,MAAM,CAAA;SAClB,CAAA;QACD,QAAQ,CAAC,EAAE;YACT,UAAU,EAAE,MAAM,CAAA;YAClB,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,MAAM,CAAA;YACf,KAAK,CAAC,EAAE,MAAM,CAAA;SACf,CAAA;QACD,SAAS,CAAC,EAAE;YACV,UAAU,CAAC,EAAE,MAAM,CAAA;YACnB,eAAe,CAAC,EAAE,MAAM,CAAA;YACxB,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,YAAY,CAAC,EAAE,MAAM,CAAA;YACrB,UAAU,CAAC,EAAE,MAAM,CAAA;SACpB,CAAA;QACD,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC5C,gBAAgB,CAAC,EAAE;YACjB,WAAW,CAAC,EAAE,MAAM,CAAA;YACpB,UAAU,EAAE,MAAM,CAAA;YAClB,MAAM,CAAC,EAAE,MAAM,CAAA;YACf,EAAE,EAAE,MAAM,CAAA;YACV,SAAS,EAAE,MAAM,CAAA;YACjB,iBAAiB,CAAC,EAAE,OAAO,CAAA;YAC3B,SAAS,CAAC,EAAE,OAAO,CAAA;SACpB,CAAA;QACD,QAAQ,CAAC,EAAE;YACT,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC3B,WAAW,CAAC,EAAE,MAAM,CAAA;YACpB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAA;YAChB,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YACzC,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YACnC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC7C,QAAQ,CAAC,EAAE,MAAM,CAAA;YACjB,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SAC5B,CAAA;QACD,YAAY,CAAC,EAAE,KAAK,CAAC;YACnB,KAAK,CAAC,EAAE,MAAM,CAAA;YACd,UAAU,CAAC,EAAE,MAAM,CAAA;YACnB,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,cAAc,CAAC,EAAE,MAAM,CAAA;YACvB,gBAAgB,CAAC,EAAE,MAAM,CAAA;YACzB,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,YAAY,CAAC,EAAE,MAAM,CAAA;YACrB,UAAU,CAAC,EAAE,MAAM,CAAA;YACnB,EAAE,CAAC,EAAE,MAAM,CAAA;YACX,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,mBAAmB,CAAC,EAAE,MAAM,CAAA;SAC7B,CAAC,CAAA;QACF,OAAO,CAAC,EAAE,KAAK,CAAC;YACd,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,KAAK,CAAC,EAAE,MAAM,CAAA;YACd,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,EAAE,CAAC,EAAE,MAAM,CAAA;YACX,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,QAAQ,CAAC,EAAE,MAAM,CAAA;YACjB,SAAS,CAAC,EAAE,MAAM,CAAA;YAClB,cAAc,CAAC,EAAE,MAAM,CAAA;YACvB,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,UAAU,CAAC,EAAE,MAAM,CAAA;SACpB,CAAC,CAAA;QACF,SAAS,CAAC,EAAE,KAAK,CAAC;YAChB,EAAE,CAAC,EAAE,MAAM,CAAA;YACX,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,IAAI,EAAE,QAAQ,GAAG,OAAO,GAAG,aAAa,GAAG,YAAY,GAAG,SAAS,CAAA;SACpE,CAAC,CAAA;QACF,QAAQ,CAAC,EAAE;YACT,MAAM,CAAC,EAAE,MAAM,CAAA;YACf,MAAM,CAAC,EAAE,MAAM,CAAA;YACf,MAAM,CAAC,EAAE,MAAM,CAAA;YACf,MAAM,CAAC,EAAE,MAAM,CAAA;YACf,KAAK,CAAC,EAAE,MAAM,CAAA;YACd,OAAO,CAAC,EAAE,MAAM,CAAA;YAChB,MAAM,CAAC,EAAE,MAAM,CAAA;YACf,cAAc,CAAC,EAAE,MAAM,CAAA;YACvB,cAAc,CAAC,EAAE,MAAM,CAAA;YACvB,WAAW,CAAC,EAAE,MAAM,CAAA;YACpB,SAAS,CAAC,EAAE,MAAM,CAAA;YAClB,KAAK,CAAC,EAAE,MAAM,CAAA;SACf,CAAA;QACD,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;KACpB,CAAC,CAAA;CACH;AAED,MAAM,CAAC,OAAO,UAAU,EAAE,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS,EAClE,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,GAC1B,WAAW,CAAC,CAAC,CAAC,CAyBhB"}
{"name": "black-blog", "version": "1.0.0", "description": "A modern novel writing and reading platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.2.0", "react": "^18.3.0", "react-dom": "^18.3.0", "@next/font": "^14.2.0", "next-auth": "^5.0.0-beta.19", "@auth/prisma-adapter": "^2.4.0", "prisma": "^5.15.0", "@prisma/client": "^5.15.0", "@reduxjs/toolkit": "^2.2.0", "react-redux": "^9.1.0", "@supabase/supabase-js": "^2.43.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@tailwindcss/typography": "^0.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.3.0", "lucide-react": "^0.395.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-button": "^1.1.0", "@radix-ui/react-toast": "^1.2.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "react-hook-form": "^7.52.0", "@hookform/resolvers": "^3.6.0", "zod": "^3.23.0"}, "devDependencies": {"typescript": "^5.5.0", "@types/node": "^20.14.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0"}, "keywords": ["nextjs", "novel", "writing", "reading", "platform"], "author": "", "license": "MIT"}
#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Black Blogs - Quick Start Setup"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Step 1: Install dependencies
print_status "Step 1: Installing dependencies..."
if npm install; then
    print_success "Dependencies installed!"
else
    print_error "Failed to install dependencies."
    exit 1
fi

# Step 2: Setup Supabase
print_status "Step 2: Setting up Supabase..."
if [ -f "scripts/setup-supabase.sh" ]; then
    ./scripts/setup-supabase.sh
else
    print_error "Supabase setup script not found."
    exit 1
fi

# Step 3: Check for environment file
print_status "Step 3: Checking environment configuration..."
if [ ! -f ".env.local" ]; then
    print_warning ".env.local not found. Creating from template..."
    if [ -f ".env.example" ]; then
        cp .env.example .env.local
        print_success "Created .env.local from template."
        print_warning "Please update .env.local with your actual credentials!"
    else
        print_error ".env.example not found. Please create .env.local manually."
    fi
else
    print_success ".env.local already exists."
fi

# Step 4: Generate Prisma client
print_status "Step 4: Generating Prisma client..."
if npx prisma generate; then
    print_success "Prisma client generated!"
else
    print_error "Failed to generate Prisma client."
    exit 1
fi

# Step 5: Push database schema
print_status "Step 5: Pushing database schema..."
if npx prisma db push; then
    print_success "Database schema pushed!"
else
    print_warning "Failed to push database schema. You may need to update your DATABASE_URL in .env.local"
fi

echo ""
echo "=================================="
print_success "Quick start setup complete!"
echo "=================================="

print_status "To start development:"
echo "1. Update .env.local with your Supabase credentials (if not done already)"
echo "2. Run 'npm run dev' to start the development server"
echo "3. Open http://localhost:3000 in your browser"

print_status "Useful commands:"
echo "• npm run dev           - Start development server"
echo "• npx prisma studio     - Open Prisma Studio"
echo "• supabase studio       - Open Supabase Studio"
echo "• supabase status       - Check Supabase status"

echo ""
print_success "Happy coding! 🎉"

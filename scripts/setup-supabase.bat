@echo off
setlocal enabledelayedexpansion

echo 🚀 Setting up Supabase for Black Blogs...
echo ==========================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

REM Check if Supabase CLI is installed
supabase --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Supabase CLI not found. Installing via npm...
    npm install -g supabase
    
    REM Verify installation
    supabase --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Failed to install Supabase CLI. Please install manually.
        pause
        exit /b 1
    ) else (
        echo [SUCCESS] Supabase CLI installed successfully!
    )
) else (
    echo [SUCCESS] Supabase CLI is already installed.
)

REM Show Supabase version
for /f "tokens=*" %%i in ('supabase --version') do set SUPABASE_VERSION=%%i
echo [INFO] Using Supabase CLI version: !SUPABASE_VERSION!

REM Check if logged in to Supabase
supabase auth status >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Not logged in to Supabase. Please login...
    supabase login
    
    supabase auth status >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Failed to login to Supabase. Please try again.
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] Already logged in to Supabase.
)

REM Initialize Supabase (if not already done)
if not exist "supabase" (
    echo [INFO] Initializing Supabase in project...
    supabase init
    echo [SUCCESS] Supabase initialized!
) else (
    echo [SUCCESS] Supabase already initialized.
)

REM Check if local Supabase is already running
supabase status >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] Local Supabase is already running.
    echo [INFO] Current status:
    supabase status
) else (
    REM Start local development
    echo [INFO] Starting local Supabase...
    echo [WARNING] This may take a few minutes on first run...
    
    supabase start
    if errorlevel 1 (
        echo [ERROR] Failed to start local Supabase. Check Docker and try again.
        pause
        exit /b 1
    ) else (
        echo [SUCCESS] Local Supabase started successfully!
    )
)

echo.
echo ==========================================
echo [SUCCESS] Supabase setup complete!
echo ==========================================

REM Get and display local credentials
echo [INFO] Local Supabase credentials:
supabase status

echo.
echo [INFO] Next steps:
echo 1. Copy the credentials above to your .env.local file
echo 2. Update your DATABASE_URL with the DB URL shown above
echo 3. Run 'npx prisma db push' to sync your schema
echo 4. Run 'npm run dev' to start your Next.js app
echo 5. Access Supabase Studio at the Studio URL shown above

echo.
echo [INFO] Useful commands:
echo • supabase status       - Check local Supabase status
echo • supabase stop         - Stop local Supabase
echo • supabase studio       - Open Supabase Studio
echo • supabase logs         - View logs
echo • supabase db reset     - Reset local database

echo.
echo [SUCCESS] Happy coding! 🎉
pause

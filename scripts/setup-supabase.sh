#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

echo "🚀 Setting up Supabase for Black Blogs..."
echo "=========================================="

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Supabase CLI is installed
if ! command_exists supabase; then
    print_warning "Supabase CLI not found. Installing..."
    
    # Detect OS and install accordingly
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command_exists brew; then
            brew install supabase/tap/supabase
        else
            print_warning "Homebrew not found. Installing via npm..."
            npm install -g supabase
        fi
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        # Windows
        if command_exists scoop; then
            scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
            scoop install supabase
        else
            print_warning "Scoop not found. Installing via npm..."
            npm install -g supabase
        fi
    else
        # Linux or other
        npm install -g supabase
    fi
    
    # Verify installation
    if command_exists supabase; then
        print_success "Supabase CLI installed successfully!"
    else
        print_error "Failed to install Supabase CLI. Please install manually."
        exit 1
    fi
else
    print_success "Supabase CLI is already installed."
fi

# Show Supabase version
SUPABASE_VERSION=$(supabase --version)
print_status "Using Supabase CLI version: $SUPABASE_VERSION"

# Login to Supabase
print_status "Logging in to Supabase..."
if ! supabase auth status >/dev/null 2>&1; then
    print_warning "Not logged in to Supabase. Please login..."
    supabase login
    
    if ! supabase auth status >/dev/null 2>&1; then
        print_error "Failed to login to Supabase. Please try again."
        exit 1
    fi
else
    print_success "Already logged in to Supabase."
fi

# Initialize Supabase (if not already done)
if [ ! -d "supabase" ]; then
    print_status "Initializing Supabase in project..."
    supabase init
    print_success "Supabase initialized!"
else
    print_success "Supabase already initialized."
fi

# Check if local Supabase is already running
if supabase status >/dev/null 2>&1; then
    print_warning "Local Supabase is already running."
    print_status "Current status:"
    supabase status
else
    # Start local development
    print_status "Starting local Supabase..."
    print_warning "This may take a few minutes on first run..."
    
    if supabase start; then
        print_success "Local Supabase started successfully!"
    else
        print_error "Failed to start local Supabase. Check Docker and try again."
        exit 1
    fi
fi

echo ""
echo "=========================================="
print_success "Supabase setup complete!"
echo "=========================================="

# Get and display local credentials
print_status "Local Supabase credentials:"
supabase status

echo ""
print_status "Next steps:"
echo "1. Copy the credentials above to your .env.local file"
echo "2. Update your DATABASE_URL with the DB URL shown above"
echo "3. Run 'npx prisma db push' to sync your schema"
echo "4. Run 'npm run dev' to start your Next.js app"
echo "5. Access Supabase Studio at the Studio URL shown above"

echo ""
print_status "Useful commands:"
echo "• supabase status       - Check local Supabase status"
echo "• supabase stop         - Stop local Supabase"
echo "• supabase studio       - Open Supabase Studio"
echo "• supabase logs         - View logs"
echo "• supabase db reset     - Reset local database"

echo ""
print_success "Happy coding! 🎉"

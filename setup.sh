#!/bin/bash

# Black Blogs - Setup Script
echo "🚀 Setting up Black Blogs platform..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Install additional dependencies that might be missing
echo "📦 Installing additional dependencies..."
npm install next-themes @types/node

# Install shadcn/ui CLI and components
echo "🎨 Setting up shadcn/ui..."
npx shadcn-ui@latest init --yes --defaults

# Install shadcn/ui components
echo "🎨 Installing UI components..."
npx shadcn-ui@latest add button
npx shadcn-ui@latest add input
npx shadcn-ui@latest add textarea
npx shadcn-ui@latest add card
npx shadcn-ui@latest add dialog
npx shadcn-ui@latest add dropdown-menu
npx shadcn-ui@latest add avatar
npx shadcn-ui@latest add toast
npx shadcn-ui@latest add badge
npx shadcn-ui@latest add tabs
npx shadcn-ui@latest add form

# Create .env.local if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating .env.local file..."
    cp .env.example .env.local
    echo "⚠️  Please update .env.local with your actual environment variables"
fi

# Generate Prisma client
echo "🗄️  Generating Prisma client..."
npx prisma generate

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update .env.local with your environment variables"
echo "2. Set up your Supabase database"
echo "3. Run 'npx prisma db push' to create database tables"
echo "4. Run 'npm run dev' to start the development server"
echo ""
echo "📚 Check the docs/ folder for detailed implementation guides"
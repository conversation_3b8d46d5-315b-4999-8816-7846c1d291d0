import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, Users, PenTool, Heart } from "lucide-react"
import Link from "next/link"

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <BookOpen className="h-16 w-16 text-primary" />
          </div>
          <h1 className="text-4xl font-bold mb-4">About Black Blogs</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            A modern platform where authors can write and publish their novels, 
            and readers can discover and enjoy amazing stories.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <PenTool className="h-6 w-6 text-primary" />
                For Authors
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Create and manage your novels</li>
                <li>• Write chapters with our rich editor</li>
                <li>• Track your writing progress</li>
                <li>• Publish when you're ready</li>
                <li>• Build your author profile</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Users className="h-6 w-6 text-primary" />
                For Readers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Discover new novels and authors</li>
                <li>• Build your personal library</li>
                <li>• Track your reading progress</li>
                <li>• Search and filter by genre</li>
                <li>• Enjoy a clean reading experience</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Mission Statement */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Heart className="h-6 w-6 text-primary" />
              Our Mission
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed">
              We believe that everyone has a story to tell and stories to discover. 
              Black Blogs provides a modern, user-friendly platform that empowers 
              authors to share their creativity and helps readers find their next 
              favorite book. Our goal is to foster a community where literature 
              thrives in the digital age.
            </p>
          </CardContent>
        </Card>

        {/* Technology Stack */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Built with Modern Technology</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="font-semibold">Next.js 14</div>
                <div className="text-muted-foreground">React Framework</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="font-semibold">Supabase</div>
                <div className="text-muted-foreground">Database</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="font-semibold">NextAuth.js</div>
                <div className="text-muted-foreground">Authentication</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="font-semibold">Tailwind CSS</div>
                <div className="text-muted-foreground">Styling</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-muted-foreground mb-6">
            Join our community of authors and readers today.
          </p>
          <div className="flex gap-4 justify-center">
            <Link href="/auth/signin">
              <Button size="lg">Sign Up</Button>
            </Link>
            <Link href="/browse">
              <Button variant="outline" size="lg">Browse Novels</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

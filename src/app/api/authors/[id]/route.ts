import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/db"
import { NovelStatus } from "@prisma/client"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params

  try {
    const author = await prisma.user.findUnique({
      where: { 
        id,
        role: "AUTHOR"
      },
      select: {
        id: true,
        name: true,
        image: true,
        bio: true,
        createdAt: true,
        novels: {
          where: { status: NovelStatus.PUBLISHED },
          select: {
            id: true,
            title: true,
            description: true,
            synopsis: true,
            genre: true,
            tags: true,
            status: true,
            publishedAt: true,
            createdAt: true,
            updatedAt: true,
            _count: { 
              select: { 
                chapters: {
                  where: { status: "PUBLISHED" }
                }
              } 
            },
          },
          orderBy: { publishedAt: "desc" },
        },
        _count: {
          select: {
            novels: {
              where: { status: NovelStatus.PUBLISHED }
            }
          }
        }
      },
    })

    if (!author) {
      return NextResponse.json({ error: "Author not found" }, { status: 404 })
    }

    // Calculate total chapters across all novels
    const totalChapters = author.novels.reduce((sum, novel) => sum + novel._count.chapters, 0)

    const authorProfile = {
      ...author,
      stats: {
        totalNovels: author._count.novels,
        totalChapters,
        memberSince: author.createdAt
      }
    }

    return NextResponse.json(authorProfile)
  } catch (error) {
    console.error("Error fetching author profile:", error)
    return NextResponse.json(
      { error: "Failed to fetch author profile" },
      { status: 500 }
    )
  }
}

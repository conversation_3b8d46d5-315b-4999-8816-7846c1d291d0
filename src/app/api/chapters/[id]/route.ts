import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

import { prisma } from "@/lib/db"
import { ChapterStatus } from "@prisma/client"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params

  try {
    const chapter = await prisma.chapter.findUnique({
      where: { id },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            authorId: true,
            status: true,
          },
        },
      },
    })

    if (!chapter) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    // Check if user can access this chapter
    const session = await getServerSession(authOptions)
    const isAuthor = session?.user?.id === chapter.novel.authorId

    // Only published chapters are accessible to non-authors
    if (chapter.status !== ChapterStatus.PUBLISHED && !isAuthor) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    // Only published novels' chapters are accessible to non-authors
    if (chapter.novel.status !== "PUBLISHED" && !isAuthor) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    return NextResponse.json(chapter)
  } catch (error) {
    console.error("Error fetching chapter:", error)
    return NextResponse.json(
      { error: "Failed to fetch chapter" },
      { status: 500 }
    )
  }
}
import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

import { prisma } from "@/lib/db"

export async function DELETE(
  request: NextRequest,
  { params }: { params: { novelId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { novelId } = params

  try {
    // Check if the entry exists
    const existingEntry = await prisma.library.findUnique({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
    })

    if (!existingEntry) {
      return NextResponse.json(
        { error: "Novel not found in library" },
        { status: 404 }
      )
    }

    // Remove from library
    await prisma.library.delete({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
    })

    return NextResponse.json({ message: "Novel removed from library" })
  } catch (error) {
    console.error("Error removing from library:", error)
    return NextResponse.json(
      { error: "Failed to remove from library" },
      { status: 500 }
    )
  }
}
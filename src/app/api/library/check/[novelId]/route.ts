import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"

import { prisma } from "@/lib/db"

export async function GET(
  request: NextRequest,
  { params }: { params: { novelId: string } }
) {
  const session = await auth()

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { novelId } = params

  try {
    const libraryEntry = await prisma.library.findUnique({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
    })

    return NextResponse.json({ inLibrary: !!libraryEntry })
  } catch (error) {
    console.error("Error checking library:", error)
    return NextResponse.json(
      { error: "Failed to check library" },
      { status: 500 }
    )
  }
}
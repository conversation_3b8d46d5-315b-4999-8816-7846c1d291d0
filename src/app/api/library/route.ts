import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

import { prisma } from "@/lib/db"

export async function GET() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const library = await prisma.library.findMany({
      where: { userId: session.user.id },
      include: {
        novel: {
          include: {
            author: { select: { id: true, name: true, image: true } },
            _count: { select: { chapters: true } },
          },
        },
      },
      orderBy: { addedAt: "desc" },
    })

    return NextResponse.json(library)
  } catch (error) {
    console.error("Error fetching library:", error)
    return NextResponse.json(
      { error: "Failed to fetch library" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { novelId } = body

    if (!novelId) {
      return NextResponse.json(
        { error: "Novel ID is required" },
        { status: 400 }
      )
    }

    // Check if novel exists and is published
    const novel = await prisma.novel.findUnique({
      where: { id: novelId },
      select: { id: true, status: true },
    })

    if (!novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    if (novel.status !== "PUBLISHED") {
      return NextResponse.json(
        { error: "Cannot add unpublished novel to library" },
        { status: 400 }
      )
    }

    // Check if already in library
    const existingEntry = await prisma.library.findUnique({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
    })

    if (existingEntry) {
      return NextResponse.json(
        { error: "Novel already in library" },
        { status: 409 }
      )
    }

    // Add to library
    const libraryEntry = await prisma.library.create({
      data: {
        userId: session.user.id,
        novelId,
      },
      include: {
        novel: {
          include: {
            author: { select: { id: true, name: true, image: true } },
            _count: { select: { chapters: true } },
          },
        },
      },
    })

    return NextResponse.json(libraryEntry, { status: 201 })
  } catch (error) {
    console.error("Error adding to library:", error)
    return NextResponse.json(
      { error: "Failed to add to library" },
      { status: 500 }
    )
  }
}
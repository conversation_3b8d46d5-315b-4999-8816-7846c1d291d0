import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

import { prisma } from "@/lib/db"
import { NovelStatus } from "@prisma/client"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params

  try {
    const novel = await prisma.novel.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            bio: true
          }
        },
        chapters: {
          where: { status: "PUBLISHED" },
          select: {
            id: true,
            title: true,
            order: true,
            createdAt: true,
          },
          orderBy: { order: "asc" },
        },
        _count: { select: { chapters: true } },
      },
    })

    if (!novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    // Only show published novels to non-authors
    const session = await getServerSession(authOptions)
    const isAuthor = session?.user?.id === novel.authorId

    if (novel.status !== NovelStatus.PUBLISHED && !isAuthor) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    return NextResponse.json(novel)
  } catch (error) {
    console.error("Error fetching novel:", error)
    return NextResponse.json(
      { error: "Failed to fetch novel" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "AUTHOR") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { id } = params

  try {
    // Verify ownership
    const existingNovel = await prisma.novel.findUnique({
      where: { id },
      select: { authorId: true },
    })

    if (!existingNovel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    if (existingNovel.authorId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const body = await request.json()
    const { title, description, synopsis, genre, tags, status } = body

    const updatedNovel = await prisma.novel.update({
      where: { id },
      data: {
        ...(title && { title: title.trim() }),
        ...(description !== undefined && { description: description?.trim() || null }),
        ...(synopsis !== undefined && { synopsis: synopsis?.trim() || null }),
        ...(genre !== undefined && { genre: genre?.trim() || null }),
        ...(tags && { tags }),
        ...(status && { status }),
        ...(status === NovelStatus.PUBLISHED && { publishedAt: new Date() }),
      },
      include: {
        author: { select: { id: true, name: true, image: true } },
        _count: { select: { chapters: true } },
      },
    })

    return NextResponse.json(updatedNovel)
  } catch (error) {
    console.error("Error updating novel:", error)
    return NextResponse.json(
      { error: "Failed to update novel" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "AUTHOR") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { id } = params

  try {
    // Verify ownership
    const existingNovel = await prisma.novel.findUnique({
      where: { id },
      select: { authorId: true },
    })

    if (!existingNovel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    if (existingNovel.authorId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    await prisma.novel.delete({
      where: { id },
    })

    return NextResponse.json({ message: "Novel deleted successfully" })
  } catch (error) {
    console.error("Error deleting novel:", error)
    return NextResponse.json(
      { error: "Failed to delete novel" },
      { status: 500 }
    )
  }
}
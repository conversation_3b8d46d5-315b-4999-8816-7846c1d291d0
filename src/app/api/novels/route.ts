import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { NovelStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get("page") || "1")
  const limit = parseInt(searchParams.get("limit") || "12")
  const genre = searchParams.get("genre")
  const search = searchParams.get("search")
  const status = searchParams.get("status") || NovelStatus.PUBLISHED

  const skip = (page - 1) * limit

  const where = {
    status: status as NovelStatus,
    ...(genre && { genre }),
    ...(search && {
      OR: [
        { title: { contains: search, mode: "insensitive" as const } },
        { description: { contains: search, mode: "insensitive" as const } },
        { author: { name: { contains: search, mode: "insensitive" as const } } },
      ],
    }),
  }

  try {
    const [novels, total] = await Promise.all([
      prisma.novel.findMany({
        where,
        include: {
          author: { select: { id: true, name: true, image: true } },
          _count: { select: { chapters: true } },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.novel.count({ where }),
    ])

    return NextResponse.json({
      novels,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching novels:", error)
    return NextResponse.json(
      { error: "Failed to fetch novels" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "AUTHOR") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { title, description, synopsis, genre, tags } = body

    // Validate required fields
    if (!title || title.trim().length === 0) {
      return NextResponse.json(
        { error: "Title is required" },
        { status: 400 }
      )
    }

    const novel = await prisma.novel.create({
      data: {
        title: title.trim(),
        description: description?.trim() || null,
        synopsis: synopsis?.trim() || null,
        genre: genre?.trim() || null,
        tags: tags || [],
        authorId: session.user.id,
        status: NovelStatus.DRAFT,
      },
      include: {
        author: { select: { id: true, name: true, image: true } },
        _count: { select: { chapters: true } },
      },
    })

    return NextResponse.json(novel, { status: 201 })
  } catch (error) {
    console.error("Error creating novel:", error)
    return NextResponse.json(
      { error: "Failed to create novel" },
      { status: 500 }
    )
  }
}
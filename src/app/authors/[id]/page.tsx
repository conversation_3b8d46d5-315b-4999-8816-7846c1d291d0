"use client"

import { useParams } from "next/navigation"
import { useGetAuthorProfileQuery } from "@/store/api/authorsApi"
import { AuthorProfile } from "@/components/author/author-profile"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { ArrowLeft, User } from "lucide-react"
import Link from "next/link"

export default function AuthorProfilePage() {
  const params = useParams()
  const authorId = params.id as string

  const { data: author, isLoading, error } = useGetAuthorProfileQuery(authorId)

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  if (error || !author) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <User className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold mb-2">Author Not Found</h2>
              <p className="text-muted-foreground mb-4">
                The author profile you're looking for doesn't exist.
              </p>
              <Link href="/browse">
                <Button>Browse Novels</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/browse">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Browse
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold">{author.name}</h1>
            <p className="text-muted-foreground">Author Profile</p>
          </div>
        </div>

        {/* Author Profile Component */}
        <AuthorProfile author={author} />
      </div>
    </div>
  )
}

"use client"

import { useParams } from "next/navigation"
import { AuthorGuard } from "@/components/auth/auth-guard"
import { ChapterForm } from "@/components/chapter/chapter-form"
import { useGetNovelQuery } from "@/store/api/novelsApi"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { ArrowLeft, BookOpen } from "lucide-react"
import Link from "next/link"

export default function NewChapterPage() {
  const params = useParams()
  const novelId = params.id as string

  const { data: novel, isLoading, error } = useGetNovelQuery(novelId)

  if (isLoading) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center">
            <LoadingSpinner />
          </div>
        </div>
      </AuthorGuard>
    )
  }

  if (error || !novel) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-2">Novel Not Found</h2>
                <p className="text-muted-foreground mb-4">
                  The novel you're looking for doesn't exist or you don't have permission to add chapters.
                </p>
                <Link href="/dashboard">
                  <Button>Back to Dashboard</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </AuthorGuard>
    )
  }

  return (
    <AuthorGuard>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Link href={`/dashboard/novels/${novelId}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Novel
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-3xl font-bold">New Chapter</h1>
              <div className="flex items-center gap-2 text-muted-foreground">
                <BookOpen className="h-4 w-4" />
                <span>{novel.title}</span>
              </div>
            </div>
          </div>

          {/* Chapter Form */}
          <Card>
            <CardHeader>
              <CardTitle>Chapter Details</CardTitle>
            </CardHeader>
            <CardContent>
              <ChapterForm novelId={novelId} />
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthorGuard>
  )
}

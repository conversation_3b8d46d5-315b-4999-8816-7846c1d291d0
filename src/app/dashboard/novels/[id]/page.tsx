"use client"

import { useParams } from "next/navigation"
import { AuthorGuard } from "@/components/auth/auth-guard"
import { NovelManagement } from "@/components/novel/novel-management"
import { useGetNovelQuery } from "@/store/api/novelsApi"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function NovelManagementPage() {
  const params = useParams()
  const novelId = params.id as string

  const { data: novel, isLoading, error } = useGetNovelQuery(novelId)

  if (isLoading) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center">
            <LoadingSpinner />
          </div>
        </div>
      </AuthorGuard>
    )
  }

  if (error || !novel) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-2">Novel Not Found</h2>
                <p className="text-muted-foreground mb-4">
                  The novel you're looking for doesn't exist or you don't have permission to manage it.
                </p>
                <Link href="/dashboard">
                  <Button>Back to Dashboard</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </AuthorGuard>
    )
  }

  return (
    <AuthorGuard>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-3xl font-bold">{novel.title}</h1>
              <p className="text-muted-foreground">
                Manage your novel and chapters
              </p>
            </div>
          </div>

          {/* Novel Management Component */}
          <NovelManagement novel={novel} />
        </div>
      </div>
    </AuthorGuard>
  )
}

"use client"

import { AuthorGuard } from "@/components/auth/auth-guard"
import { NovelForm } from "@/components/novel/novel-form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function NewNovelPage() {
  return (
    <AuthorGuard>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">Create New Novel</h1>
              <p className="text-muted-foreground">
                Start writing your next masterpiece
              </p>
            </div>
          </div>

          {/* Novel Form */}
          <Card>
            <CardHeader>
              <CardTitle>Novel Details</CardTitle>
            </CardHeader>
            <CardContent>
              <NovelForm />
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthorGuard>
  )
}

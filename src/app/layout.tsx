import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers/session-provider'
import { ReduxProvider } from '@/components/providers/redux-provider'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Black Blogs - Novel Writing & Reading Platform',
  description: 'A modern platform for authors to write and publish novels, and readers to discover and enjoy content.',
  keywords: ['novels', 'writing', 'reading', 'books', 'authors', 'stories'],
  authors: [{ name: 'Black Blogs Team' }],
  openGraph: {
    title: 'Black Blogs - Novel Writing & Reading Platform',
    description: 'A modern platform for authors to write and publish novels, and readers to discover and enjoy content.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Black Blogs - Novel Writing & Reading Platform',
    description: 'A modern platform for authors to write and publish novels, and readers to discover and enjoy content.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Providers>
            <ReduxProvider>
              <div className="relative flex min-h-screen flex-col">
                <Header />
                <main className="flex-1">{children}</main>
                <Footer />
              </div>
              <Toaster />
            </ReduxProvider>
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  )
}
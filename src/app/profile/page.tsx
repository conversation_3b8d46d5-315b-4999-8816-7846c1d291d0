"use client"

import { useSession } from "next-auth/react"
import { AuthGuard } from "@/components/auth/auth-guard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { User, Mail, Calendar, BookOpen, PenTool } from "lucide-react"
import { formatDate } from "@/lib/utils"

function ProfileContent() {
  const { data: session } = useSession()

  if (!session?.user) {
    return null
  }

  const user = session.user

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Avatar className="h-20 w-20">
            <AvatarImage src={user.image || ""} alt={user.name || "User"} />
            <AvatarFallback className="text-lg">
              {user.name?.[0]?.toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>
          <div className="space-y-1">
            <h1 className="text-3xl font-bold">{user.name || "Anonymous User"}</h1>
            <p className="text-muted-foreground">{user.email}</p>
            <Badge variant={user.role === "AUTHOR" ? "default" : "secondary"}>
              {user.role?.toLowerCase()}
            </Badge>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Your account details and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{user.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  Member since {formatDate(new Date())}
                </span>
              </div>
              <div className="pt-4">
                <Button variant="outline" className="w-full">
                  Edit Profile
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {user.role === "AUTHOR" ? (
                  <PenTool className="h-5 w-5" />
                ) : (
                  <BookOpen className="h-5 w-5" />
                )}
                {user.role === "AUTHOR" ? "Author Stats" : "Reading Stats"}
              </CardTitle>
              <CardDescription>
                {user.role === "AUTHOR"
                  ? "Your writing progress and achievements"
                  : "Your reading activity and favorites"
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {user.role === "AUTHOR" ? (
                <>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Novels</span>
                    <span className="text-sm font-medium">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Chapters</span>
                    <span className="text-sm font-medium">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Published</span>
                    <span className="text-sm font-medium">0</span>
                  </div>
                  <div className="pt-4">
                    <Button className="w-full">
                      Go to Dashboard
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Library</span>
                    <span className="text-sm font-medium">0 novels</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Reading</span>
                    <span className="text-sm font-medium">0 active</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Completed</span>
                    <span className="text-sm font-medium">0 novels</span>
                  </div>
                  <div className="pt-4 space-y-2">
                    <Button className="w-full">
                      Browse Novels
                    </Button>
                    <Button variant="outline" className="w-full">
                      Become an Author
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default function ProfilePage() {
  return (
    <AuthGuard requireAuth={true}>
      <ProfileContent />
    </AuthGuard>
  )
}
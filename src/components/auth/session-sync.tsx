"use client"

import { useEffect } from "react"
import { useSession } from "next-auth/react"
import { useDispatch } from "react-redux"
import { setUser, clearUser, setLoading } from "@/store/slices/authSlice"
import type { AppDispatch } from "@/lib/store"

export function SessionSync() {
  const { data: session, status } = useSession()
  const dispatch = useDispatch<AppDispatch>()

  useEffect(() => {
    if (status === "loading") {
      dispatch(setLoading(true))
    } else if (status === "authenticated" && session?.user) {
      dispatch(setUser({
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        image: session.user.image,
        role: session.user.role,
      }))
    } else {
      dispatch(clearUser())
    }
  }, [session, status, dispatch])

  return null // This component doesn't render anything
}
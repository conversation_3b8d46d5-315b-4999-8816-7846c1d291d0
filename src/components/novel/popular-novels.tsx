export function PopularNovels() {
  return (
    <section className="py-12">
      <div className="space-y-6">
        <div className="text-center space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Popular This Week</h2>
          <p className="text-muted-foreground">
            Trending stories that readers can't put down
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {/* Placeholder cards - will be replaced with actual data */}
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="bg-card rounded-lg border p-4 space-y-3">
              <div className="aspect-[3/4] bg-muted rounded-md"></div>
              <div className="space-y-2">
                <h3 className="font-semibold line-clamp-2">Popular Novel {i + 1}</h3>
                <p className="text-sm text-muted-foreground">by Popular Author</p>
                <p className="text-sm text-muted-foreground line-clamp-3">
                  This is a sample description of a popular novel that would appear here...
                </p>
                <div className="flex items-center gap-2">
                  <span className="text-xs bg-secondary px-2 py-1 rounded">Romance</span>
                  <span className="text-xs text-muted-foreground">{Math.floor(Math.random() * 50) + 10} chapters</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
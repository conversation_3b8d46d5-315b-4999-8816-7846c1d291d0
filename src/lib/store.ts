import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'
import { novelsApi } from '@/store/api/novelsApi'
import { chaptersApi } from '@/store/api/chaptersApi'
import { libraryApi } from '@/store/api/libraryApi'
import { authorsApi } from '@/store/api/authorsApi'
import { authSlice } from '@/store/slices/authSlice'
import { uiSlice } from '@/store/slices/uiSlice'

export const store = configureStore({
  reducer: {
    // API slices
    [novelsApi.reducerPath]: novelsApi.reducer,
    [chaptersApi.reducerPath]: chaptersApi.reducer,
    [libraryApi.reducerPath]: libraryApi.reducer,
    [authorsApi.reducerPath]: authorsApi.reducer,

    // Regular slices
    auth: authSlice.reducer,
    ui: uiSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      novelsApi.middleware,
      chaptersApi.middleware,
      libraryApi.middleware,
      authorsApi.middleware
    ),
})

setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
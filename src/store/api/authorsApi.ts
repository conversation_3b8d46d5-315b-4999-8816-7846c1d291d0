import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { NovelWithAuthor } from '@/types/novel'

export interface AuthorProfile {
  id: string
  name: string | null
  image: string | null
  bio: string | null
  createdAt: string
  novels: NovelWithAuthor[]
  stats: {
    totalNovels: number
    totalChapters: number
    memberSince: string
  }
}

export const authorsApi = createApi({
  reducerPath: 'authorsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/authors',
  }),
  tagTypes: ['Author'],
  endpoints: (builder) => ({
    getAuthorProfile: builder.query<AuthorProfile, string>({
      query: (id) => `/${id}`,
      providesTags: (result, error, id) => [{ type: 'Author', id }],
    }),
  }),
})

export const {
  useGetAuthorProfileQuery,
} = authorsApi

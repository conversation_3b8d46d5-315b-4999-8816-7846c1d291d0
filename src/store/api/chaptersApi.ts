import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Chapter, ChapterStatus } from '@prisma/client'

export interface CreateChapterRequest {
  title: string
  content: string
}

export interface UpdateChapterRequest {
  title?: string
  content?: string
  status?: ChapterStatus
}

export const chaptersApi = createApi({
  reducerPath: 'chaptersApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
  }),
  tagTypes: ['Chapter', 'NovelChapters'],
  endpoints: (builder) => ({
    getChapters: builder.query<Chapter[], string>({
      query: (novelId) => `/novels/${novelId}/chapters`,
      providesTags: (result, error, novelId) => [
        { type: 'NovelChapters', id: novelId },
      ],
    }),

    getChapter: builder.query<Chapter, string>({
      query: (id) => `/chapters/${id}`,
      providesTags: (result, error, id) => [{ type: 'Chapter', id }],
    }),

    createChapter: builder.mutation<Chapter, {
      novelId: string
      data: CreateChapterRequest
    }>({
      query: ({ novelId, data }) => ({
        url: `/novels/${novelId}/chapters`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { novelId }) => [
        { type: 'NovelChapters', id: novelId },
      ],
    }),

    updateChapter: builder.mutation<Chapter, {
      id: string
      data: UpdateChapterRequest
    }>({
      query: ({ id, data }) => ({
        url: `/chapters/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Chapter', id },
      ],
    }),

    deleteChapter: builder.mutation<void, string>({
      query: (id) => ({
        url: `/chapters/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['NovelChapters'],
    }),

    reorderChapters: builder.mutation<void, {
      novelId: string
      chapters: { id: string; order: number }[]
    }>({
      query: ({ novelId, chapters }) => ({
        url: `/novels/${novelId}/chapters/reorder`,
        method: 'PUT',
        body: { chapters },
      }),
      invalidatesTags: (result, error, { novelId }) => [
        { type: 'NovelChapters', id: novelId },
      ],
    }),
  }),
})

export const {
  useGetChaptersQuery,
  useGetChapterQuery,
  useCreateChapterMutation,
  useUpdateChapterMutation,
  useDeleteChapterMutation,
  useReorderChaptersMutation,
} = chaptersApi
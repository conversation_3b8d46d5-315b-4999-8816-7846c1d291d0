import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Novel, NovelStatus } from '@prisma/client'

export interface NovelWithAuthor extends Novel {
  author: { id: string; name: string }
  _count: { chapters: number }
}

export interface NovelsResponse {
  novels: NovelWithAuthor[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface CreateNovelRequest {
  title: string
  description?: string
  synopsis?: string
  genre?: string
  tags?: string[]
}

export const novelsApi = createApi({
  reducerPath: 'novelsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/novels',
  }),
  tagTypes: ['Novel', 'AuthorNovels'],
  endpoints: (builder) => ({
    // Public endpoints
    getNovels: builder.query<NovelsResponse, {
      page?: number
      limit?: number
      genre?: string
      search?: string
    }>({
      query: (params) => ({
        url: '',
        params,
      }),
      providesTags: ['Novel'],
    }),

    getNovel: builder.query<NovelWithAuthor, string>({
      query: (id) => `/${id}`,
      providesTags: (result, error, id) => [{ type: 'Novel', id }],
    }),

    getFeaturedNovels: builder.query<NovelWithAuthor[], void>({
      query: () => '/featured',
      providesTags: ['Novel'],
    }),

    // Author endpoints
    getAuthorNovels: builder.query<NovelWithAuthor[], void>({
      query: () => '/author',
      providesTags: ['AuthorNovels'],
    }),

    createNovel: builder.mutation<NovelWithAuthor, CreateNovelRequest>({
      query: (novel) => ({
        url: '',
        method: 'POST',
        body: novel,
      }),
      invalidatesTags: ['AuthorNovels'],
    }),

    updateNovel: builder.mutation<NovelWithAuthor, {
      id: string
      data: Partial<CreateNovelRequest>
    }>({
      query: ({ id, data }) => ({
        url: `/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Novel', id },
        'AuthorNovels',
      ],
    }),

    deleteNovel: builder.mutation<void, string>({
      query: (id) => ({
        url: `/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['AuthorNovels'],
    }),

    publishNovel: builder.mutation<NovelWithAuthor, {
      id: string
      status: NovelStatus
    }>({
      query: ({ id, status }) => ({
        url: `/${id}/publish`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Novel', id },
        'AuthorNovels',
        'Novel',
      ],
    }),
  }),
})

export const {
  useGetNovelsQuery,
  useGetNovelQuery,
  useGetFeaturedNovelsQuery,
  useGetAuthorNovelsQuery,
  useCreateNovelMutation,
  useUpdateNovelMutation,
  useDeleteNovelMutation,
  usePublishNovelMutation,
} = novelsApi
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { UserRole } from '@prisma/client'

interface AuthState {
  isAuthenticated: boolean
  user: {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
    role: UserRole
  } | null
  loading: boolean
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: true,
}

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<AuthState['user']>) => {
      state.user = action.payload
      state.isAuthenticated = !!action.payload
      state.loading = false
    },
    clearUser: (state) => {
      state.user = null
      state.isAuthenticated = false
      state.loading = false
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    },
    updateUserRole: (state, action: PayloadAction<UserRole>) => {
      if (state.user) {
        state.user.role = action.payload
      }
    },
  },
})

export const {
  setUser,
  clearUser,
  setLoading,
  updateUserRole,
} = authSlice.actions
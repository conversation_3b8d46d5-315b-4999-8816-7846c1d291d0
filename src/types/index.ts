import type { Novel, Chapter, User, Library } from "@prisma/client"

// Extended types with relations
export interface NovelWithAuthor extends Novel {
  author: Pick<User, "id" | "name" | "image">
  _count: {
    chapters: number
  }
}

export interface NovelWithChapters extends Novel {
  author: Pick<User, "id" | "name" | "image">
  chapters: Chapter[]
  _count: {
    chapters: number
  }
}

export interface ChapterWithNovel extends Chapter {
  novel: Pick<Novel, "id" | "title" | "authorId">
}

export interface LibraryWithNovel extends Library {
  novel: NovelWithAuthor
}

// API Response types
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Form types
export interface CreateNovelForm {
  title: string
  description?: string
  synopsis?: string
  genre?: string
  tags?: string[]
}

export interface CreateChapterForm {
  title: string
  content: string
}

export interface UpdateChapterForm {
  title?: string
  content?: string
}

// Search and filter types
export interface NovelFilters {
  search?: string
  genre?: string
  status?: string
  authorId?: string
  page?: number
  limit?: number
  sortBy?: "createdAt" | "updatedAt" | "title"
  sortOrder?: "asc" | "desc"
}

// UI State types
export interface UIState {
  theme: "light" | "dark" | "system"
  sidebarOpen: boolean
  loading: boolean
  error: string | null
}

// Reading progress types
export interface ReadingProgress {
  userId: string
  novelId: string
  chapterId: string
  progress: number // percentage
  lastReadAt: Date
}